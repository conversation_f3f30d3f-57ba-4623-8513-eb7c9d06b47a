name: 跨平台构建语雀下载器

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: '版本号 (例如: v2.1.0)'
        required: true
        default: 'v2.1.0'

jobs:
  build-windows:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v4

    - name: 设置 Python 环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller
    
    - name: 构建 Windows 可执行文件
      run: |
        pyinstaller yuque_downloader_windows.spec
    
    - name: 创建 Windows 分发包
      run: |
        mkdir windows-dist
        copy dist\yuque-downloader.exe windows-dist\
        copy run-yuque-downloader-windows.bat windows-dist\start.bat
        copy README.md windows-dist\
        copy LICENSE windows-dist\
        copy INSTALL.md windows-dist\
        copy requirements.txt windows-dist\
        mkdir windows-dist\downloads
        mkdir windows-dist\logs
        mkdir windows-dist\drivers
        echo. > windows-dist\downloads\.gitkeep
        echo. > windows-dist\logs\.gitkeep
        echo. > windows-dist\drivers\.gitkeep
    
    - name: 压缩 Windows 包
      run: |
        powershell Compress-Archive -Path windows-dist\* -DestinationPath yuque-downloader-windows.zip
    
    - name: 上传 Windows 构建产物
      uses: actions/upload-artifact@v4
      with:
        name: yuque-downloader-windows
        path: yuque-downloader-windows.zip

  build-macos:
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v4

    - name: 设置 Python 环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller
    
    - name: 构建 macOS 可执行文件
      run: |
        pyinstaller yuque_downloader_terminal.spec
    
    - name: 创建 macOS 分发包
      run: |
        mkdir -p macos-dist
        cp dist/yuque-downloader macos-dist/
        cp run-yuque-downloader-v2.1.0.sh macos-dist/start.sh
        cp README.md macos-dist/
        cp LICENSE macos-dist/
        cp INSTALL.md macos-dist/
        cp requirements.txt macos-dist/
        mkdir -p macos-dist/downloads
        mkdir -p macos-dist/logs
        mkdir -p macos-dist/drivers
        touch macos-dist/downloads/.gitkeep
        touch macos-dist/logs/.gitkeep
        touch macos-dist/drivers/.gitkeep
        chmod +x macos-dist/yuque-downloader
        chmod +x macos-dist/start.sh
    
    - name: 压缩 macOS 包
      run: |
        cd macos-dist && tar -czf ../yuque-downloader-macos.tar.gz *
    
    - name: 上传 macOS 构建产物
      uses: actions/upload-artifact@v4
      with:
        name: yuque-downloader-macos
        path: yuque-downloader-macos.tar.gz

  build-linux:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: 设置 Python 环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: 安装系统依赖
      run: |
        sudo apt-get update
        sudo apt-get install -y wget unzip
    
    - name: 安装 Python 依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller
    
    - name: 构建 Linux 可执行文件
      run: |
        pyinstaller yuque_downloader_terminal.spec
    
    - name: 创建 Linux 分发包
      run: |
        mkdir -p linux-dist
        cp dist/yuque-downloader linux-dist/
        cp run-yuque-downloader-v2.1.0.sh linux-dist/start.sh
        cp README.md linux-dist/
        cp LICENSE linux-dist/
        cp INSTALL.md linux-dist/
        cp requirements.txt linux-dist/
        mkdir -p linux-dist/downloads
        mkdir -p linux-dist/logs
        mkdir -p linux-dist/drivers
        touch linux-dist/downloads/.gitkeep
        touch linux-dist/logs/.gitkeep
        touch linux-dist/drivers/.gitkeep
        chmod +x linux-dist/yuque-downloader
        chmod +x linux-dist/start.sh
    
    - name: 压缩 Linux 包
      run: |
        cd linux-dist && tar -czf ../yuque-downloader-linux.tar.gz *
    
    - name: 上传 Linux 构建产物
      uses: actions/upload-artifact@v4
      with:
        name: yuque-downloader-linux
        path: yuque-downloader-linux.tar.gz

  create-release:
    needs: [build-windows, build-macos, build-linux]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    steps:
    - name: 下载所有构建产物
      uses: actions/download-artifact@v4
    
    - name: 创建发布
      uses: softprops/action-gh-release@v1
      with:
        files: |
          yuque-downloader-windows/yuque-downloader-windows.zip
          yuque-downloader-macos/yuque-downloader-macos.tar.gz
          yuque-downloader-linux/yuque-downloader-linux.tar.gz
        body: |
          ## 🚀 语雀下载器 ${{ github.ref_name }}
          
          ### 📦 下载说明
          - **Windows**: 下载 `yuque-downloader-windows.zip`，解压后运行 `start.bat`
          - **macOS**: 下载 `yuque-downloader-macos.tar.gz`，解压后运行 `./start.sh`
          - **Linux**: 下载 `yuque-downloader-linux.tar.gz`，解压后运行 `./start.sh`
          
          ### ✨ 功能特性
          - 📋 支持批量下载语雀知识库
          - 📊 支持 Excel 文档下载
          - 🔧 智能文件名修复
          - 📝 详细下载日志
          
          ### 🛠️ 系统要求
          - Windows 10+ / macOS 10.14+ / Ubuntu 18.04+
          - Chrome 浏览器
          - 网络连接
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
