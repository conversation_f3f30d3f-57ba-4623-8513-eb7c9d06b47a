# 语雀下载器项目 .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec.bak

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定文件
chrome_user_data/
drivers/chromedriver*
logs/
downloads/
*.log
*.json.bak

# 临时文件和缓存
*.tmp
*.temp
.cache/
.pytest_cache/

# 构建产物
dist/
build/
*.zip
*.tar.gz
*.dmg
*.exe

# 测试文件
test_downloads/
test_*/

# 敏感信息
yuque_cookies.json
yuque_headers.json
yuque_session.json
*.key
*.crt

# 文档生成
docs/_build/

# 备份文件
*.bak
*.backup
*~

# 本地配置
local_config.py
.env.local

# 跨平台构建输出
cross-platform-builds/
windows-dist/
macos-dist/
linux-dist/

# 下载日志
download_log_*.txt

# 响应数据文件（测试用）
book_stacks_response.json
user_books_response.json
intercepted_books.json
