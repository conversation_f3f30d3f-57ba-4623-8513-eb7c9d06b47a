# 🚀 语雀下载器跨平台打包指南

## 📋 概述

由于 PyInstaller 的限制，**无法在 macOS ARM64 系统上直接生成 Windows .exe 文件**。本指南提供三种可行的跨平台打包解决方案。

## 🎯 解决方案对比

| 方案 | 优点 | 缺点 | 推荐度 |
|------|------|------|--------|
| GitHub Actions | 自动化、支持多平台、免费 | 需要 GitHub 仓库 | ⭐⭐⭐⭐⭐ |
| Docker 容器 | 本地控制、可重复构建 | 配置复杂、需要 Docker | ⭐⭐⭐⭐ |
| 虚拟机/云服务 | 直接构建、简单直接 | 需要额外资源 | ⭐⭐⭐ |

## 🏆 方案一：GitHub Actions（推荐）

### 优势
- ✅ 完全自动化的多平台构建
- ✅ 免费使用（公开仓库）
- ✅ 支持 Windows、macOS、Linux
- ✅ 自动发布到 GitHub Releases

### 使用步骤

1. **推送代码到 GitHub**
```bash
git add .
git commit -m "添加跨平台构建配置"
git push origin main
```

2. **创建版本标签触发构建**
```bash
git tag v2.1.0
git push origin v2.1.0
```

3. **查看构建进度**
- 访问 GitHub 仓库的 Actions 页面
- 等待构建完成（约 10-15 分钟）

4. **下载构建产物**
- 构建完成后在 Releases 页面下载
- 包含 Windows、macOS、Linux 三个版本

### 手动触发构建
```bash
# 在 GitHub 仓库页面
Actions → 跨平台构建语雀下载器 → Run workflow
```

## 🐳 方案二：Docker 容器构建

### 前置要求
- 安装 Docker Desktop
- 确保 Docker 正在运行

### 使用步骤

1. **给脚本添加执行权限**
```bash
chmod +x docker/build-cross-platform.sh
```

2. **运行跨平台构建**
```bash
./docker/build-cross-platform.sh
```

3. **查看构建结果**
```bash
ls -la cross-platform-builds/
```

### 仅构建 Windows 版本
```bash
# 构建 Docker 镜像
docker build -f docker/Dockerfile.windows -t yuque-downloader-windows-builder .

# 运行构建
docker run --rm -v "$(pwd)/output:/output" yuque-downloader-windows-builder
```

## 🖥️ 方案三：Windows 环境直接构建

### 在 Windows 系统中

1. **安装 Python 3.8+**
```cmd
# 下载并安装 Python
https://www.python.org/downloads/windows/
```

2. **克隆项目**
```cmd
git clone <your-repo-url>
cd yuque-downloader
```

3. **运行构建脚本**
```cmd
python build_windows_package.py
```

### 使用云服务器

1. **租用 Windows 云服务器**
   - AWS EC2 Windows 实例
   - Azure Windows VM
   - 阿里云 Windows ECS

2. **远程连接并构建**
```cmd
# 在云服务器中执行
git clone <your-repo-url>
cd yuque-downloader
python build_windows_package.py
```

## 📦 构建产物说明

### Windows 版本
```
yuque-downloader-windows-YYYYMMDD_HHMMSS.zip
├── yuque-downloader.exe     # 主程序
├── start.bat               # 启动脚本
├── 使用说明.txt             # 用户指南
├── README.md               # 详细文档
├── LICENSE                 # 许可证
├── downloads/              # 下载目录
├── logs/                   # 日志目录
└── drivers/                # 驱动目录
```

### macOS 版本
```
yuque-downloader-macos-YYYYMMDD_HHMMSS.tar.gz
├── yuque-downloader        # 主程序
├── start.sh               # 启动脚本
├── README.md              # 详细文档
├── LICENSE                # 许可证
├── downloads/             # 下载目录
├── logs/                  # 日志目录
└── drivers/               # 驱动目录
```

## 🔧 配置文件说明

### PyInstaller 配置
- `yuque_downloader_windows.spec` - Windows 专用配置
- `yuque_downloader_terminal.spec` - macOS/Linux 配置

### 关键配置项
```python
# Windows 特定配置
console=True                    # 显示控制台
upx=True                       # 启用 UPX 压缩
target_arch=None               # 自动检测架构

# 依赖包含
hiddenimports=[
    'selenium', 'seleniumwire', 
    'requests', 'tqdm', 'beautifulsoup4'
]

# 排除不需要的包
excludes=[
    'tkinter', 'matplotlib', 'numpy', 
    'pandas', 'PIL', 'cv2'
]
```

## 🧪 测试和验证

### 本地测试
```bash
# 测试可执行文件
./dist/yuque-downloader --help

# 测试完整包
cd yuque-downloader-windows-*/
./start.bat
```

### 虚拟机测试
1. 在 Windows 虚拟机中测试 .exe 文件
2. 在 Linux 虚拟机中测试 Linux 版本
3. 验证所有依赖是否正确打包

## ❓ 常见问题

### Q: GitHub Actions 构建失败？
A: 检查以下项目：
- 确保所有依赖在 requirements.txt 中
- 检查 .spec 文件路径是否正确
- 查看 Actions 日志中的具体错误

### Q: Docker 构建失败？
A: 可能的解决方案：
- 确保 Docker Desktop 正在运行
- 检查网络连接（需要下载 Python）
- 增加 Docker 内存限制

### Q: Windows 包在其他机器上无法运行？
A: 检查以下项目：
- 目标机器是否为 Windows 10+
- 是否有杀毒软件阻止
- 尝试"以管理员身份运行"

### Q: 包体积过大？
A: 优化建议：
- 在 .spec 文件中添加更多 excludes
- 启用 UPX 压缩
- 移除不必要的数据文件

## 📞 技术支持

如果遇到构建问题：

1. **查看详细日志**
   - GitHub Actions: 在 Actions 页面查看日志
   - Docker: 使用 `docker logs` 命令
   - 本地构建: 查看控制台输出

2. **常见解决方案**
   - 更新 PyInstaller: `pip install --upgrade pyinstaller`
   - 清理缓存: 删除 `build/` 和 `dist/` 目录
   - 检查依赖: `pip install -r requirements.txt`

3. **获取帮助**
   - 提交 GitHub Issue
   - 包含完整的错误日志
   - 说明操作系统和 Python 版本

---

💡 **推荐流程**: 使用 GitHub Actions 进行自动化构建，本地使用 Docker 进行测试验证。
