# 🚀 GitHub Actions 自动化构建配置指南

## 📋 概述

本指南将帮你配置 GitHub Actions 自动化构建语雀下载器的跨平台版本（Windows、macOS、Linux）。

## 🎯 功能特性

- ✅ 自动构建三平台版本（Windows .exe、macOS、Linux）
- ✅ 版本标签触发自动构建和发布
- ✅ 手动触发构建支持
- ✅ 自动创建 GitHub Release
- ✅ 完整的分发包（包含文档、启动脚本等）

## 📦 第一步：准备项目文件

### 检查必要文件
确保项目包含以下关键文件：

```bash
# 核心配置文件
.github/workflows/build-cross-platform.yml  ✅ 已存在
yuque_downloader_windows.spec              ✅ 已存在
yuque_downloader_terminal.spec             ✅ 已存在
requirements.txt                           ✅ 已存在
.gitignore                                 ✅ 已创建

# 启动脚本
run-yuque-downloader-windows.bat           ✅ 已存在
run-yuque-downloader-v2.1.0.sh            ✅ 已存在

# 文档文件
README.md                                  ✅ 已存在
LICENSE                                    ✅ 已存在
INSTALL.md                                 ✅ 已存在
```

## 🔧 第二步：初始化 Git 仓库

### 1. 初始化本地仓库
```bash
cd /Users/<USER>/PycharmProjects/PythonProject2

# 初始化 Git 仓库
git init

# 添加所有文件
git add .

# 创建初始提交
git commit -m "初始化语雀下载器项目 - 支持跨平台自动构建"
```

### 2. 创建 GitHub 仓库

#### 方法一：使用 GitHub CLI（推荐）
```bash
# 安装 GitHub CLI（如果未安装）
brew install gh

# 登录 GitHub
gh auth login

# 创建仓库并推送
gh repo create yuque-downloader --public --source=. --remote=origin --push
```

#### 方法二：手动创建
1. 访问 [GitHub](https://github.com)
2. 点击右上角 "+" → "New repository"
3. 仓库名称：`yuque-downloader`
4. 设置为 Public（免费使用 Actions）
5. 不要初始化 README（我们已有文件）
6. 点击 "Create repository"

### 3. 连接远程仓库（方法二需要）
```bash
# 添加远程仓库（替换 YOUR_USERNAME）
git remote add origin https://github.com/YOUR_USERNAME/yuque-downloader.git

# 推送代码
git branch -M main
git push -u origin main
```

## ⚙️ 第三步：配置 GitHub Actions 权限

### 1. 启用 Actions
1. 进入 GitHub 仓库页面
2. 点击 "Settings" 标签
3. 左侧菜单选择 "Actions" → "General"
4. 在 "Actions permissions" 部分选择：
   - ✅ "Allow all actions and reusable workflows"

### 2. 配置 GITHUB_TOKEN 权限
在同一页面的 "Workflow permissions" 部分：
- ✅ 选择 "Read and write permissions"
- ✅ 勾选 "Allow GitHub Actions to create and approve pull requests"

### 3. 保存设置
点击页面底部的 "Save" 按钮

## 🚀 第四步：测试自动化构建

### 1. 手动触发构建（测试）
```bash
# 方法一：在 GitHub 网页界面
# 1. 进入仓库页面
# 2. 点击 "Actions" 标签
# 3. 选择 "跨平台构建语雀下载器" 工作流
# 4. 点击 "Run workflow" 按钮
# 5. 输入版本号（如：v2.1.0）
# 6. 点击绿色 "Run workflow" 按钮

# 方法二：使用 GitHub CLI
gh workflow run "跨平台构建语雀下载器" -f version=v2.1.0
```

### 2. 版本标签触发构建
```bash
# 创建版本标签
git tag v2.1.0

# 推送标签（这将自动触发构建）
git push origin v2.1.0
```

### 3. 监控构建过程
1. 进入 GitHub 仓库的 "Actions" 页面
2. 查看正在运行的工作流
3. 点击工作流名称查看详细日志
4. 构建时间约 10-15 分钟

## 📦 第五步：验证构建结果

### 1. 检查构建状态
构建完成后应该看到：
- ✅ build-windows 作业成功
- ✅ build-macos 作业成功  
- ✅ build-linux 作业成功
- ✅ create-release 作业成功（仅标签触发时）

### 2. 下载构建产物

#### 从 Actions 页面下载（手动触发）
1. 进入完成的工作流
2. 滚动到页面底部 "Artifacts" 部分
3. 下载各平台的构建产物：
   - `yuque-downloader-windows`
   - `yuque-downloader-macos`
   - `yuque-downloader-linux`

#### 从 Releases 页面下载（标签触发）
1. 进入仓库的 "Releases" 页面
2. 查看最新发布的版本
3. 下载对应平台的文件：
   - `yuque-downloader-windows.zip`
   - `yuque-downloader-macos.tar.gz`
   - `yuque-downloader-linux.tar.gz`

### 3. 验证构建产物
```bash
# 解压并检查 Windows 版本
unzip yuque-downloader-windows.zip
ls -la yuque-downloader-windows/
# 应该包含：yuque-downloader.exe, start.bat, README.md 等

# 解压并检查 macOS 版本
tar -xzf yuque-downloader-macos.tar.gz
ls -la yuque-downloader-macos/
# 应该包含：yuque-downloader, start.sh, README.md 等
```

## 🔄 第六步：日常使用流程

### 发布新版本
```bash
# 1. 更新代码
git add .
git commit -m "更新功能：添加新特性"
git push origin main

# 2. 创建新版本标签
git tag v2.1.1
git push origin v2.1.1

# 3. 自动触发构建和发布
# GitHub Actions 会自动：
# - 构建三平台版本
# - 创建 GitHub Release
# - 上传分发包
```

### 测试构建（不发布）
```bash
# 使用 GitHub CLI 手动触发
gh workflow run "跨平台构建语雀下载器" -f version=test-v2.1.1

# 或在 GitHub 网页界面手动触发
```

## 🛠️ 故障排除

### 常见问题和解决方案

#### 1. 构建失败：权限错误
```
Error: Resource not accessible by integration
```
**解决方案**：
- 检查 Settings → Actions → General → Workflow permissions
- 确保选择了 "Read and write permissions"

#### 2. 构建失败：依赖安装错误
```
ERROR: Could not find a version that satisfies the requirement
```
**解决方案**：
- 检查 `requirements.txt` 文件格式
- 确保所有依赖版本号正确

#### 3. Windows 构建失败：PyInstaller 错误
```
FileNotFoundError: [Errno 2] No such file or directory: 'seleniumwire/ca.crt'
```
**解决方案**：
- 这是正常的，配置文件已处理此情况
- 检查构建日志，通常会继续成功

#### 4. Release 创建失败
```
Error: Release already exists
```
**解决方案**：
```bash
# 删除已存在的标签和 Release
git tag -d v2.1.0
git push origin :refs/tags/v2.1.0
# 然后重新创建标签
```

### 查看详细日志
1. 进入 Actions 页面
2. 点击失败的工作流
3. 点击失败的作业
4. 展开失败的步骤查看详细错误信息

## 📋 版本标签命名规范

推荐使用语义化版本号：
```bash
v2.1.0    # 主要版本.次要版本.修订版本
v2.1.1    # 修复版本
v2.2.0    # 新功能版本
v3.0.0    # 重大更新版本
```

## 🎉 完成！

配置完成后，你的语雀下载器项目将具备：
- ✅ 自动化跨平台构建
- ✅ 版本管理和发布
- ✅ 用户友好的下载体验
- ✅ 完整的文档和说明

每次推送版本标签时，GitHub Actions 会自动构建并发布三个平台的可执行文件，用户可以直接下载使用，无需安装 Python 环境。
