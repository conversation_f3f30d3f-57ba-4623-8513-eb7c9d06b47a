# 🛠️ GitHub Actions 故障排除指南

## 🚨 常见问题和解决方案

### 1. 权限错误 (403 Forbidden)

#### 问题表现
```
⚠️ GitHub release failed with status: 403
Error: Resource not accessible by integration
```

#### 解决方案
1. **检查仓库设置**：
   - 访问 `Settings` → `Actions` → `General`
   - 确保 "Workflow permissions" 设置为 "Read and write permissions"
   - 勾选 "Allow GitHub Actions to create and approve pull requests"

2. **检查工作流权限**：
   ```yaml
   permissions:
     contents: write
     packages: write
     actions: read
   ```

3. **检查分支保护规则**：
   - 确保没有阻止 Actions 创建 Release 的规则

### 2. Windows PowerShell 命令错误

#### 问题表现
```
The term 'echo.' is not recognized as a name of a cmdlet
Error: Process completed with exit code 1
```

#### 解决方案
使用 PowerShell Core 兼容命令：
```yaml
- name: 创建文件
  run: |
    New-Item -Path "file.txt" -ItemType File -Force
  shell: pwsh
```

### 3. Artifact 上传失败

#### 问题表现
```
actions/upload-artifact@v3 is deprecated
Error: Artifact upload failed
```

#### 解决方案
更新到最新版本：
```yaml
- uses: actions/upload-artifact@v4
  with:
    name: artifact-name
    path: file-path
```

### 4. 依赖安装失败

#### 问题表现
```
ERROR: Could not find a version that satisfies the requirement
```

#### 解决方案
1. **检查 requirements.txt 格式**
2. **更新 pip**：
   ```yaml
   - name: 安装依赖
     run: |
       python -m pip install --upgrade pip
       pip install -r requirements.txt
   ```

### 5. PyInstaller 构建失败

#### 问题表现
```
FileNotFoundError: [Errno 2] No such file or directory
```

#### 解决方案
1. **检查 .spec 文件路径**
2. **确保所有依赖文件存在**
3. **添加隐藏导入**：
   ```python
   hiddenimports=[
       'selenium',
       'seleniumwire',
       # 其他必要模块
   ]
   ```

## 🔍 调试技巧

### 1. 启用详细日志
```yaml
- name: 调试步骤
  run: |
    echo "当前目录内容:"
    ls -la
    echo "Python 版本:"
    python --version
    echo "已安装包:"
    pip list
```

### 2. 检查文件是否存在
```yaml
- name: 检查文件
  run: |
    if [ -f "yuque_downloader_windows.spec" ]; then
      echo "✅ Spec 文件存在"
    else
      echo "❌ Spec 文件不存在"
      exit 1
    fi
```

### 3. 保存调试信息
```yaml
- name: 上传调试信息
  if: failure()
  uses: actions/upload-artifact@v4
  with:
    name: debug-logs
    path: |
      *.log
      build/
      dist/
```

## 📋 检查清单

### 构建前检查
- [ ] 所有必要文件存在
- [ ] requirements.txt 格式正确
- [ ] .spec 文件配置正确
- [ ] 仓库权限设置正确

### 构建后检查
- [ ] 所有作业成功完成
- [ ] Artifacts 正确上传
- [ ] Release 成功创建
- [ ] 文件大小合理

### 权限检查
- [ ] Workflow permissions: Read and write
- [ ] Actions permissions: Allow all actions
- [ ] 分支保护规则不冲突

## 🆘 获取帮助

### 1. 查看详细日志
1. 进入 GitHub 仓库的 Actions 页面
2. 点击失败的工作流
3. 点击失败的作业
4. 展开失败的步骤查看详细错误

### 2. 常用调试命令
```bash
# 查看工作流状态
gh run list

# 查看特定运行的日志
gh run view RUN_ID --log

# 重新运行失败的工作流
gh run rerun RUN_ID
```

### 3. 本地测试
```bash
# 验证项目配置
python verify_github_actions_setup.py

# 本地测试构建
python test_build_locally.py
```

## 📞 联系支持

如果问题仍然存在：
1. 检查 GitHub Status 页面
2. 查看 GitHub Community 论坛
3. 提交 GitHub Support 请求
4. 查看项目的 Issues 页面

---

💡 **提示**: 大多数问题都与权限设置或文件路径相关。首先检查这两个方面通常能解决 80% 的问题。
