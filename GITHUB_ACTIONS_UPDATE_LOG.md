# GitHub Actions 更新日志

## 🔧 2025-01-08 - Actions 版本更新

### 问题描述
GitHub Actions 工作流中使用的 `actions/upload-artifact@v3` 和 `actions/download-artifact@v3` 已被弃用，导致构建失败。

### 错误信息
```
This request has been automatically failed because it uses a deprecated version of actions/upload-artifact: v3. 
Learn more: https://github.blog/changelog/2024-04-16-deprecation-notice-v3-of-the-artifact-actions/
```

### 修复内容

#### 1. 更新 Artifact Actions
- `actions/upload-artifact@v3` → `actions/upload-artifact@v4`
- `actions/download-artifact@v3` → `actions/download-artifact@v4`

#### 2. 更新 Python Setup Action
- `actions/setup-python@v4` → `actions/setup-python@v5`

#### 3. 保持最新版本
- `actions/checkout@v4` (已是最新)

### 更新后的版本对照

| Action | 旧版本 | 新版本 | 状态 |
|--------|--------|--------|------|
| actions/checkout | v4 | v4 | ✅ 最新 |
| actions/setup-python | v4 | v5 | ✅ 已更新 |
| actions/upload-artifact | v3 | v4 | ✅ 已更新 |
| actions/download-artifact | v3 | v4 | ✅ 已更新 |
| softprops/action-gh-release | v1 | v1 | ✅ 稳定版 |

### 影响范围
- ✅ Windows 构建作业
- ✅ macOS 构建作业  
- ✅ Linux 构建作业
- ✅ Release 创建作业

### 验证步骤
1. 提交更新后的工作流文件
2. 创建测试标签触发构建
3. 验证所有平台构建成功
4. 确认 Artifacts 正常上传和下载
5. 验证 Release 自动创建功能

### 测试命令
```bash
# 提交更新
git add .github/workflows/build-cross-platform.yml
git commit -m "fix: 更新 GitHub Actions 到最新版本

- 修复 actions/upload-artifact@v3 弃用问题
- 更新 actions/setup-python 到 v5
- 确保所有 actions 使用最新稳定版本"

# 推送更新
git push origin main

# 创建测试标签
git tag v2.1.1
git push origin v2.1.1
```

### 预期结果
- ✅ 所有构建作业成功完成
- ✅ Artifacts 正常上传到 Actions 页面
- ✅ Release 自动创建并包含所有平台的分发包
- ✅ 无弃用警告信息

### 相关链接
- [GitHub Actions Artifact v4 发布说明](https://github.blog/changelog/2024-04-16-deprecation-notice-v3-of-the-artifact-actions/)
- [actions/upload-artifact@v4 文档](https://github.com/actions/upload-artifact/tree/v4)
- [actions/setup-python@v5 文档](https://github.com/actions/setup-python/tree/v5)

## 🔧 2025-01-08 - Windows PowerShell 命令修复

### 问题描述
Windows 构建作业中使用的 `echo.` 命令在 PowerShell 中不被识别，导致构建失败。

### 错误信息
```
The term 'echo.' is not recognized as a name of a cmdlet, function, script file, or executable program.
Error: Process completed with exit code 1.
```

### 修复内容

#### 1. 替换不兼容的命令
- `echo. > file` → `New-Item -Path "file" -ItemType File -Force`
- `mkdir dir` → `New-Item -Path "dir" -ItemType Directory -Force`
- `copy src dest` → `Copy-Item "src" "dest"`

#### 2. 明确指定 PowerShell Shell
- 添加 `shell: pwsh` 确保使用 PowerShell Core

#### 3. 使用引号包围路径
- 防止路径中的特殊字符导致问题

### 修复后的命令对照

| 操作 | 旧命令 | 新命令 |
|------|--------|--------|
| 创建目录 | `mkdir windows-dist` | `New-Item -Path "windows-dist" -ItemType Directory -Force` |
| 复制文件 | `copy dist\file windows-dist\` | `Copy-Item "dist\file" "windows-dist\"` |
| 创建空文件 | `echo. > file` | `New-Item -Path "file" -ItemType File -Force` |
| 压缩文件 | `powershell Compress-Archive` | `Compress-Archive -Path "..." -DestinationPath "..."` |

### 注意事项
1. **向后兼容性**: v4 版本的 artifact actions 与 v3 完全兼容
2. **性能改进**: v4 版本提供更好的性能和稳定性
3. **安全更新**: 新版本包含重要的安全修复
4. **未来维护**: 建议定期检查和更新 actions 版本
5. **PowerShell 兼容性**: 使用 PowerShell Core 命令确保跨平台兼容

### 维护建议
- 每季度检查 GitHub Actions 版本更新
- 订阅 GitHub Blog 获取弃用通知
- 使用 Dependabot 自动更新 actions 版本
- 在测试环境中验证更新后再应用到生产环境
- 优先使用 PowerShell Core 命令而非传统 CMD 命令
