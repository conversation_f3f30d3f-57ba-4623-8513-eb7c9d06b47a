# 🚀 GitHub Actions 快速开始指南

## 📋 5分钟快速设置

### 第一步：验证项目准备情况
```bash
# 检查所有必要文件是否齐全
python verify_github_actions_setup.py
```

### 第二步：自动化设置（推荐）
```bash
# 运行自动化设置脚本
./setup_github_actions.sh
```

### 第三步：手动配置 GitHub 仓库权限
1. 访问你的 GitHub 仓库设置页面
2. 进入 `Settings` → `Actions` → `General`
3. 配置以下选项：
   - **Actions permissions**: 选择 `Allow all actions and reusable workflows`
   - **Workflow permissions**: 选择 `Read and write permissions`
   - **勾选**: `Allow GitHub Actions to create and approve pull requests`
4. 点击 `Save` 保存设置

### 第四步：测试自动化构建
```bash
# 方法一：创建版本标签（推荐）
git tag v2.1.0
git push origin v2.1.0

# 方法二：手动触发（测试用）
gh workflow run "跨平台构建语雀下载器" -f version=v2.1.0
```

## 🎯 预期结果

### 构建过程（约10-15分钟）
- ✅ **build-windows**: 构建 Windows .exe 文件
- ✅ **build-macos**: 构建 macOS 可执行文件  
- ✅ **build-linux**: 构建 Linux 可执行文件
- ✅ **create-release**: 创建 GitHub Release（仅标签触发）

### 构建产物
- 📦 **yuque-downloader-windows.zip** (约 50-80MB)
  - `yuque-downloader.exe` - 主程序
  - `start.bat` - 启动脚本
  - 完整文档和目录结构

- 📦 **yuque-downloader-macos.tar.gz** (约 40-60MB)
  - `yuque-downloader` - 主程序
  - `start.sh` - 启动脚本
  - 完整文档和目录结构

- 📦 **yuque-downloader-linux.tar.gz** (约 40-60MB)
  - `yuque-downloader` - 主程序
  - `start.sh` - 启动脚本
  - 完整文档和目录结构

## 📥 下载和使用

### 用户下载方式
1. **从 GitHub Releases 下载**（版本标签触发）
   - 访问: `https://github.com/YOUR_USERNAME/yuque-downloader/releases`
   - 下载对应平台的文件

2. **从 Actions 页面下载**（手动触发）
   - 访问: `https://github.com/YOUR_USERNAME/yuque-downloader/actions`
   - 点击完成的工作流
   - 在 "Artifacts" 部分下载

### 用户使用方式
```bash
# Windows 用户
# 1. 解压 yuque-downloader-windows.zip
# 2. 双击运行 start.bat

# macOS/Linux 用户
# 1. 解压 yuque-downloader-macos.tar.gz 或 yuque-downloader-linux.tar.gz
# 2. 在终端中运行:
chmod +x start.sh
./start.sh
```

## 🔄 日常发布流程

### 发布新版本
```bash
# 1. 更新代码
git add .
git commit -m "feat: 添加新功能"
git push origin main

# 2. 创建版本标签
git tag v2.1.1
git push origin v2.1.1

# 3. 自动触发构建和发布
# GitHub Actions 会自动：
# - 构建三平台版本
# - 创建 GitHub Release
# - 上传分发包
```

### 测试构建（不发布）
```bash
# 使用 GitHub CLI 手动触发
gh workflow run "跨平台构建语雀下载器" -f version=test-v2.1.1

# 或在 GitHub 网页界面手动触发
```

## 🛠️ 故障排除

### 常见问题

#### 1. 权限错误
```
Error: Resource not accessible by integration
```
**解决方案**: 检查 GitHub 仓库的 Actions 权限设置

#### 2. 构建失败
```
Error: Could not find a version that satisfies the requirement
```
**解决方案**: 检查 `requirements.txt` 文件格式和依赖版本

#### 3. 文件不存在错误
```
Error: File not found: yuque_downloader_windows.spec
```
**解决方案**: 运行 `python verify_github_actions_setup.py` 检查文件

### 查看详细日志
1. 进入 GitHub 仓库的 Actions 页面
2. 点击失败的工作流
3. 点击失败的作业
4. 展开失败的步骤查看错误信息

## 📞 获取帮助

### 检查工具
```bash
# 验证项目设置
python verify_github_actions_setup.py

# 查看构建状态
gh run list

# 查看最新 Release
gh release list
```

### 重要链接
- 📦 **仓库地址**: `https://github.com/YOUR_USERNAME/yuque-downloader`
- 🏗️ **Actions 页面**: `https://github.com/YOUR_USERNAME/yuque-downloader/actions`
- 📋 **设置页面**: `https://github.com/YOUR_USERNAME/yuque-downloader/settings/actions`
- 📦 **Releases 页面**: `https://github.com/YOUR_USERNAME/yuque-downloader/releases`

## 🎉 完成！

设置完成后，你的语雀下载器将具备：
- ✅ 自动化跨平台构建
- ✅ 版本管理和发布
- ✅ 用户友好的下载体验
- ✅ 完整的文档和说明

每次推送版本标签时，GitHub Actions 会自动构建并发布三个平台的可执行文件，用户可以直接下载使用，无需安装 Python 环境。
