"""
语雀API客户端模块
负责与语雀API的交互，包括知识库列表、文档列表和文档导出
"""

import time
import json
import logging
import requests
from typing import Dict, List, Optional, Any
from config import YUQUE_API_BASE, API_ENDPOINTS, DOWNLOAD_CONFIG
from api_result import APIResult
from utils import normalize_books_data

logger = logging.getLogger(__name__)


class YuqueAPIClient:
    """语雀API客户端"""
    
    def __init__(self, webdriver_manager):
        self.webdriver_manager = webdriver_manager
        self.session = requests.Session()
        self._setup_session()
    
    def _setup_session(self):
        """设置请求会话"""
        # 从WebDriver管理器获取捕获的请求头
        headers = self.webdriver_manager.get_headers()
        self.session.headers.update(headers)
        logger.info(f"已设置 {len(headers)} 个请求头")
    
    def _update_cookies_from_driver(self):
        """从WebDriver更新cookies到requests session"""
        if self.webdriver_manager.driver:
            cookies = self.webdriver_manager.driver.get_cookies()
            for cookie in cookies:
                self.session.cookies.set(cookie['name'], cookie['value'])

    def _update_headers_from_driver(self):
        """从WebDriver更新请求头到requests session"""
        headers = self.webdriver_manager.get_headers()
        self.session.headers.update(headers)
    
    def _make_request(self, method: str, url: str, **kwargs) -> APIResult:
        """发送HTTP请求"""
        try:
            # 更新会话信息
            self._update_cookies_from_driver()
            self._update_headers_from_driver()
            
            # 动态检测和更新API基础URL和Referer
            current_url = self.webdriver_manager.driver.current_url
            self._update_api_base_url(current_url)
            
            # 确保关键请求头存在
            self._ensure_critical_headers(url)
            
            response = self.session.request(method, url, timeout=30, **kwargs)

            # 检查响应状态码
            if response.status_code >= 400:
                # 尝试解析错误响应中的JSON消息
                error_msg = self._extract_error_message(response)
                logger.error(f"请求失败 {method} {url} (状态码: {response.status_code}): {error_msg}")
                return APIResult.error_result(error_msg, response.status_code, url, method)

            response.raise_for_status()

            if response.headers.get('content-type', '').startswith('application/json'):
                data = response.json()
            else:
                data = {'content': response.content, 'headers': response.headers}

            return APIResult.success_result(data, response.status_code, url, method)

        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败 {method} {url}: {e}")
            status_code = getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None
            error_msg = str(e)

            # 如果有响应，尝试提取错误消息
            if hasattr(e, 'response') and e.response is not None:
                extracted_msg = self._extract_error_message(e.response)
                if extracted_msg != str(e):
                    error_msg = extracted_msg

            return APIResult.error_result(error_msg, status_code, url, method)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return APIResult.error_result(f"JSON解析失败: {e}", None, url, method)
        except Exception as e:
            logger.error(f"请求异常 {method} {url}: {e}")
            return APIResult.error_result(str(e), None, url, method)

    def _update_api_base_url(self, current_url: str):
        """根据当前URL动态更新API基础URL"""
        global YUQUE_API_BASE
        
        if 'fjzx.yuque.com' in current_url:
            YUQUE_API_BASE = "https://fjzx.yuque.com/api"
        else:
            YUQUE_API_BASE = "https://www.yuque.com/api"
        
        logger.debug(f"API基础URL更新为: {YUQUE_API_BASE}")

    def _ensure_critical_headers(self, url: str):
        """确保关键请求头存在"""
        current_url = self.webdriver_manager.driver.current_url
        
        # 设置正确的Referer头
        if 'fjzx.yuque.com' in current_url:
            referer = "https://fjzx.yuque.com/"
        else:
            referer = "https://www.yuque.com/"
        
        # 更新session的headers
        critical_headers = {
            'Referer': referer,
            'Origin': referer.rstrip('/'),
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }
        
        # 从WebDriver捕获的headers中获取CSRF token相关信息
        captured_headers = self.webdriver_manager.get_headers()
        
        # 添加CSRF相关头部
        if 'X-CSRF-Token' in captured_headers:
            critical_headers['X-CSRF-Token'] = captured_headers['X-CSRF-Token']
        if 'X-Auth-Token' in captured_headers:
            critical_headers['X-Auth-Token'] = captured_headers['X-Auth-Token']
        
        # 特别处理导出API请求
        if '/export' in url:
            # 导出请求需要特殊的头部
            critical_headers.update({
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            })
            
            # 尝试从页面获取最新的CSRF token
            csrf_token = self._get_csrf_token_from_page()
            if csrf_token:
                critical_headers['X-CSRF-Token'] = csrf_token
                logger.info(f"使用页面CSRF token: {csrf_token[:10]}...")
        
        self.session.headers.update(critical_headers)
        logger.debug(f"更新关键请求头，包含 {len(critical_headers)} 个头部")

    def _get_csrf_token_from_page(self) -> Optional[str]:
        """从当前页面获取CSRF token"""
        try:
            # 尝试从meta标签获取CSRF token
            csrf_token = self.webdriver_manager.driver.execute_script("""
                var token = document.querySelector('meta[name="csrf-token"]');
                if (token) return token.getAttribute('content');
                
                // 尝试从其他可能的位置获取
                var scripts = document.querySelectorAll('script');
                for (var i = 0; i < scripts.length; i++) {
                    var script = scripts[i];
                    if (script.innerHTML.includes('csrf') || script.innerHTML.includes('token')) {
                        var match = script.innerHTML.match(/['"]([\w\-]{20,})['"]/);
                        if (match) return match[1];
                    }
                }
                
                // 尝试从window对象获取
                if (window.csrf_token) return window.csrf_token;
                if (window.yuque && window.yuque.csrf_token) return window.yuque.csrf_token;
                
                return null;
            """)
            
            if csrf_token:
                logger.info(f"从页面获取到CSRF token: {csrf_token[:10]}...")
                return csrf_token
            
        except Exception as e:
            logger.warning(f"从页面获取CSRF token失败: {e}")
        
        return None

    def _extract_error_message(self, response) -> str:
        """从响应中提取错误消息"""
        try:
            # 尝试解析JSON响应
            if response.headers.get('content-type', '').startswith('application/json'):
                data = response.json()

                # 检查常见的错误消息字段
                if isinstance(data, dict):
                    # 优先使用message字段
                    if 'message' in data:
                        return data['message']
                    # 其次使用error字段
                    elif 'error' in data:
                        return data['error']
                    # 检查data.message字段
                    elif 'data' in data and isinstance(data['data'], dict) and 'message' in data['data']:
                        return data['data']['message']
                    # 检查错误描述字段
                    elif 'error_description' in data:
                        return data['error_description']
                    # 如果有status和message组合
                    elif 'status' in data and 'message' in data:
                        return f"状态码 {data['status']}: {data['message']}"

                # 如果没有找到特定字段，返回整个JSON字符串
                return json.dumps(data, ensure_ascii=False)

            # 如果不是JSON，返回响应文本
            return response.text[:200] if response.text else f"HTTP {response.status_code}"

        except Exception as e:
            # 如果解析失败，返回基本错误信息
            return f"HTTP {response.status_code}: {str(e)}"
    
    def get_book_stacks(self) -> APIResult:
        """获取知识库列表"""
        url = YUQUE_API_BASE + API_ENDPOINTS["book_stacks"]
        logger.info("获取知识库列表...")

        result = self._make_request('GET', url)
        if result.success and result.data:
            # 使用标准化函数处理API响应
            books = normalize_books_data(result.data)
            logger.info(f"获取到 {len(books)} 个知识库")
            logger.debug(f"知识库数据类型: {type(books)}, 内容: {books}")
            return APIResult.success_result(books, result.status_code, url, 'GET')

        # 如果API请求失败，尝试从WebDriver拦截的请求中获取
        intercept_result = self._get_book_stacks_from_intercept()
        if intercept_result:
            # 标准化拦截的数据
            books = normalize_books_data(intercept_result)
            logger.debug(f"拦截请求标准化后数据类型: {type(books)}, 内容: {books}")
            return APIResult.success_result(books, 200, url, 'GET')

        return APIResult.error_result("无法获取知识库列表", result.status_code, url, 'GET')

    def get_user_books(self, offset: int = 0, limit: int = 100, query: str = "", user_type: str = "Group") -> APIResult:
        """获取用户知识库列表 - 混合认证模式"""
        logger.info(f"开始获取用户知识库列表 (limit={limit})")

        # 第一阶段：拦截真实请求获取认证信息
        logger.info("第一阶段：拦截真实请求获取认证信息...")
        intercepted_auth = self._intercept_user_books_auth()

        # 第二阶段：构造优化的API请求
        logger.info("第二阶段：构造优化的API请求...")

        # 构建URL参数（强制使用传入的limit值）
        params = {
            "offset": offset,
            "limit": limit,  # 确保使用传入的limit值
            "query": query,
            "user_type": user_type
        }

        # 构建完整URL
        base_url = YUQUE_API_BASE + API_ENDPOINTS["user_books"]
        param_string = "&".join([f"{k}={v}" for k, v in params.items()])
        url = f"{base_url}?{param_string}"

        logger.info(f"构造的API请求URL: {url}")
        logger.info(f"请求参数: offset={offset}, limit={limit}, query='{query}', user_type='{user_type}'")

        # 准备请求头
        headers = {}
        if intercepted_auth and intercepted_auth.get('headers'):
            headers = intercepted_auth['headers'].copy()
            logger.info("使用拦截到的认证信息")
        else:
            logger.warning("未能拦截到认证信息，使用默认请求头")

        # 添加缓存控制头避免304响应
        headers.update({
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        })

        # 移除可能导致304的头
        headers.pop('If-None-Match', None)
        headers.pop('If-Modified-Since', None)

        logger.info(f"最终请求头包含缓存控制: Cache-Control={headers.get('Cache-Control')}")

        # 发送请求
        result = self._make_request_with_headers('GET', url, headers)

        if result.success and result.data:
            # 处理user_books API响应
            books = self._normalize_user_books_data(result.data)
            logger.info(f"✅ 成功获取到 {len(books)} 个用户知识库")
            return APIResult.success_result(books, result.status_code, url, 'GET')
        else:
            logger.error(f"❌ API请求失败: {result.error_msg if result else '未知错误'}")

        # 错误处理：如果混合模式失败，回退到原有方式
        logger.info("混合模式失败，回退到原有的直接API调用方式...")
        fallback_result = self._make_request('GET', url)
        if fallback_result.success and fallback_result.data:
            books = self._normalize_user_books_data(fallback_result.data)
            logger.info(f"回退模式成功获取到 {len(books)} 个用户知识库")
            return APIResult.success_result(books, fallback_result.status_code, url, 'GET')

        return APIResult.error_result("无法获取用户知识库列表", result.status_code if result else None, url, 'GET')

    def _intercept_user_books_auth(self) -> Optional[Dict]:
        """拦截真实的user_books请求获取认证信息"""
        try:
            logger.info("开始拦截user_books请求获取认证信息...")

            if not self.webdriver_manager or not self.webdriver_manager.driver:
                logger.warning("WebDriver不可用，跳过拦截")
                return None

            driver = self.webdriver_manager.driver

            # 清空之前的请求记录
            if hasattr(driver, 'requests'):
                driver.requests.clear()

            # 确保在正确的页面
            current_url = driver.current_url
            if 'dashboard/books' not in current_url:
                logger.info("导航到知识库页面...")
                if 'fjzx.yuque.com' in current_url:
                    target_url = "https://fjzx.yuque.com/dashboard/books"
                else:
                    target_url = "https://www.yuque.com/dashboard/books"
                driver.get(target_url)
                time.sleep(3)

            # 触发页面刷新以产生API请求
            logger.info("刷新页面以触发API请求...")
            driver.refresh()
            time.sleep(2)

            # 分析拦截到的请求
            if hasattr(driver, 'requests'):
                for request in driver.requests:
                    if 'user_books' in request.url and request.response:
                        logger.info(f"找到user_books请求: {request.url}")

                        # 提取请求头
                        headers = {}
                        for name, value in request.headers.items():
                            headers[name] = value

                        logger.info("成功提取认证信息")
                        return {
                            'headers': headers,
                            'url': request.url,
                            'method': request.method
                        }

            logger.warning("未能拦截到user_books请求")
            return None

        except Exception as e:
            logger.error(f"拦截认证信息失败: {e}")
            return None

    def _make_request_with_headers(self, method: str, url: str, custom_headers: Dict) -> APIResult:
        """使用自定义请求头发送HTTP请求"""
        try:
            # 备份原始headers
            original_headers = self.session.headers.copy()

            # 更新headers
            self.session.headers.update(custom_headers)

            logger.info(f"发送请求: {method} {url}")
            logger.debug(f"使用请求头: {dict(self.session.headers)}")

            response = self.session.request(method, url, timeout=30)

            logger.info(f"响应状态码: {response.status_code}")

            # 恢复原始headers
            self.session.headers = original_headers

            # 检查响应状态码
            if response.status_code >= 400:
                error_msg = self._extract_error_message(response)
                logger.error(f"请求失败 {method} {url} (状态码: {response.status_code}): {error_msg}")
                return APIResult.error_result(error_msg, response.status_code, url, method)

            response.raise_for_status()

            if response.headers.get('content-type', '').startswith('application/json'):
                data = response.json()
            else:
                data = {'content': response.content, 'headers': response.headers}

            return APIResult.success_result(data, response.status_code, url, method)

        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败 {method} {url}: {e}")
            status_code = getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None
            error_msg = str(e)
            return APIResult.error_result(error_msg, status_code, url, method)
        except Exception as e:
            logger.error(f"请求异常 {method} {url}: {e}")
            return APIResult.error_result(str(e), None, url, method)

    def _normalize_user_books_data(self, data: List[Dict]) -> List[Dict]:
        """标准化用户知识库数据格式"""
        try:
            normalized_books = []

            for item in data:
                if isinstance(item, dict):
                    # 提取关键信息
                    book = {
                        "id": item.get("id"),  # 这就是book_id
                        "name": item.get("name", "未知知识库"),
                        "description": item.get("description", ""),
                        "public": item.get("public", 0),
                        "items_count": item.get("items_count", 0),
                        "slug": item.get("slug", ""),
                        "user_id": item.get("user_id"),
                        "organization_id": item.get("organization_id"),
                        "created_at": item.get("created_at"),
                        "updated_at": item.get("updated_at"),
                        "user": item.get("user", {}),
                        "type": item.get("type", "Book"),
                        # 保留原始数据以备后用
                        "_raw": item
                    }
                    normalized_books.append(book)

            return normalized_books

        except Exception as e:
            logger.error(f"标准化用户知识库数据失败: {e}")
            return []

    def _get_user_books_from_intercept(self, params: Dict) -> Optional[List[Dict]]:
        """从拦截的请求中获取用户知识库列表"""
        try:
            # 清空之前的请求历史
            self.webdriver_manager.clear_requests()

            # 检查当前页面
            current_url = self.webdriver_manager.driver.current_url
            logger.info(f"当前页面: {current_url}")

            # 触发user_books API请求
            logger.info("开始实时监听user_books API请求...")
            return self._wait_for_user_books_request(params)

        except Exception as e:
            logger.error(f"从拦截请求获取用户知识库失败: {e}")
            return None

    def _wait_for_user_books_request(self, params: Dict) -> Optional[List[Dict]]:
        """等待并获取user_books请求"""
        max_wait_time = 8
        check_interval = 0.05
        waited_time = 0

        print("🔍 正在获取用户知识库列表...")

        # 首先检查是否已经有拦截到的请求
        existing_requests = self.webdriver_manager.get_intercepted_requests("user_books")
        for request in existing_requests:
            if request.response and request.response.status_code == 200:
                try:
                    data = self._parse_intercepted_response(request)
                    if data:
                        print("✅ 发现已存在的用户知识库数据")
                        return data
                except Exception as e:
                    logger.error(f"解析已存在请求失败: {e}")

        # 触发API请求
        self._trigger_user_books_request(params)

        # 等待响应
        while waited_time < max_wait_time:
            requests = self.webdriver_manager.get_intercepted_requests("user_books")

            for request in requests:
                if request.response and request.response.status_code == 200:
                    try:
                        data = self._parse_intercepted_response(request)
                        if data:
                            print("✅ 成功获取用户知识库数据")
                            return data
                    except Exception as e:
                        logger.error(f"解析拦截响应失败: {e}")

            time.sleep(check_interval)
            waited_time += check_interval

            # 进度提示
            if int(waited_time) % 1 == 0 and waited_time > 0 and waited_time < max_wait_time:
                print(f"⏳ 正在获取用户知识库... ({waited_time:.0f}/{max_wait_time}秒)")

        print("❌ 获取用户知识库超时")
        return None

    def _trigger_user_books_request(self, params: Dict):
        """触发user_books API请求"""
        try:
            # 构建参数字符串
            param_string = "&".join([f"{k}={v}" for k, v in params.items()])

            logger.debug("JavaScript触发user_books API请求")
            self.webdriver_manager.driver.execute_script(f"""
                // 触发user_books API请求
                fetch('/api/mine/user_books?{param_string}', {{
                    method: 'GET',
                    headers: {{
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }}
                }}).then(response => response.json())
                  .then(data => console.log('user_books API响应:', data))
                  .catch(error => console.error('user_books API错误:', error));
            """)

            time.sleep(0.1)  # 短暂等待请求发出

        except Exception as e:
            logger.error(f"触发user_books API请求失败: {e}")

    def _get_book_stacks_from_intercept(self) -> Optional[List[Dict]]:
        """从拦截的请求中获取知识库列表"""
        try:
            # 清空之前的请求历史
            self.webdriver_manager.clear_requests()

            # 检查当前页面状态
            current_url = self.webdriver_manager.driver.current_url
            logger.info(f"当前页面URL: {current_url}")

            # 如果已经在知识库页面，直接开始拦截
            if 'dashboard/books' in current_url:
                logger.info("已在知识库页面，开始拦截API请求...")
                print("📄 已在知识库页面，正在获取数据...")

                # 先检查是否已有数据
                existing_requests = self.webdriver_manager.get_intercepted_requests("book_stacks")
                for request in existing_requests:
                    if request.response and request.response.status_code == 200:
                        try:
                            data = self._parse_intercepted_response(request)
                            if data:
                                print("✅ 发现已有知识库数据，立即使用")
                                return data
                        except Exception as e:
                            logger.error(f"解析已存在请求失败: {e}")

                # 如果没有数据，触发页面刷新
                print("🔄 刷新页面获取最新数据...")
                self.webdriver_manager.driver.refresh()
                time.sleep(1)  # 减少等待时间
            else:
                # 如果不在正确页面，尝试导航
                logger.info("不在知识库页面，尝试导航...")
                if not self._ensure_correct_page():
                    logger.error("无法导航到正确的知识库页面")
                    return None

            # 实时监听API请求
            logger.info("开始实时监听book_stacks API请求...")
            return self._wait_for_book_stacks_request()

        except Exception as e:
            logger.error(f"从拦截请求获取知识库失败: {e}")
            return None

    def _ensure_correct_page(self) -> bool:
        """确保浏览器在正确的知识库页面，处理重定向问题"""
        from config import YUQUE_DASHBOARD_URL
        target_url = YUQUE_DASHBOARD_URL
        max_attempts = 3
        wait_time = 2

        for attempt in range(max_attempts):
            current_url = self.webdriver_manager.driver.current_url
            logger.info(f"当前页面URL: {current_url}")

            # 检查是否已经在正确页面
            if 'dashboard/books' in current_url:
                logger.info("已在正确的知识库页面")
                return True

            # 检查是否需要登录
            if 'login' in current_url.lower():
                logger.info("检测到登录页面，请在浏览器中完成登录")
                input("请在浏览器中完成登录，然后按回车继续...")
                # 登录后重新导航
                continue

            # 导航到目标页面
            logger.info(f"第 {attempt + 1}/{max_attempts} 次尝试导航到知识库页面...")
            self.webdriver_manager.driver.get(target_url)

            # 等待页面加载
            time.sleep(wait_time)

            # 检查是否被重定向
            new_url = self.webdriver_manager.driver.current_url
            logger.info(f"导航后的URL: {new_url}")

            if 'dashboard/books' in new_url:
                logger.info("成功导航到知识库页面")
                return True
            elif new_url != current_url:
                logger.warning(f"页面被重定向到: {new_url}")
                # 如果被重定向到dashboard，尝试强制导航
                if 'dashboard' in new_url and 'books' not in new_url:
                    logger.info("检测到重定向到dashboard，尝试强制导航到books页面")
                    # 使用JavaScript强制导航
                    try:
                        self.webdriver_manager.driver.execute_script(f"window.location.href = '{target_url}';")
                        time.sleep(wait_time)
                        final_url = self.webdriver_manager.driver.current_url
                        if 'dashboard/books' in final_url:
                            logger.info("JavaScript导航成功")
                            return True
                    except Exception as e:
                        logger.warning(f"JavaScript导航失败: {e}")

            # 增加等待时间，给页面更多加载时间
            wait_time += 1

        logger.error(f"经过 {max_attempts} 次尝试，仍无法导航到正确页面")
        return False

    def _wait_for_book_stacks_request(self) -> Optional[List[Dict]]:
        """实时等待并获取book_stacks请求 - 优化版：获取到数据立即返回"""
        max_wait_time = 8  # 进一步减少最大等待时间到8秒
        check_interval = 0.05  # 每0.05秒检查一次，极高响应速度
        waited_time = 0

        print("🔍 正在获取知识库列表...")

        # 首先检查是否已经有拦截到的请求
        logger.info("检查是否已有拦截的book_stacks请求...")
        existing_requests = self.webdriver_manager.get_intercepted_requests("book_stacks")
        for request in existing_requests:
            if request.response and request.response.status_code == 200:
                try:
                    data = self._parse_intercepted_response(request)
                    if data:
                        print("✅ 发现已存在的知识库数据，立即使用")
                        logger.info("发现已存在的book_stacks请求，直接使用")
                        return data
                except Exception as e:
                    logger.error(f"解析已存在请求失败: {e}")

        # 如果没有现有请求，尝试触发API请求
        logger.info("未发现现有请求，尝试触发API请求...")
        self._trigger_book_stacks_request()

        # 快速检查循环 - 一旦获取到数据立即返回
        while waited_time < max_wait_time:
            # 检查是否有新的book_stacks请求
            requests = self.webdriver_manager.get_intercepted_requests("book_stacks")

            for request in requests:
                if request.response and request.response.status_code == 200:
                    try:
                        data = self._parse_intercepted_response(request)
                        if data:
                            print(f"✅ 成功获取知识库数据！(耗时: {waited_time:.1f}秒)")
                            logger.info(f"实时拦截成功，等待时间: {waited_time:.1f}秒")
                            return data
                    except Exception as e:
                        logger.error(f"解析拦截请求失败: {e}")

            # 极短等待后继续检查
            time.sleep(check_interval)
            waited_time += check_interval

            # 每1秒输出一次等待状态（减少日志输出）
            if int(waited_time) % 1 == 0 and waited_time > 0 and waited_time < max_wait_time:
                print(f"⏳ 正在获取知识库... ({waited_time:.0f}/{max_wait_time}秒)")
                # 每3秒重新触发一次（更频繁的重试）
                if waited_time % 3 == 0:
                    logger.debug("重新触发API请求...")
                    self._trigger_book_stacks_request()

        print("❌ 获取知识库超时，请检查网络连接或登录状态")
        logger.error(f"等待超时({max_wait_time}秒)，未能拦截到book_stacks请求")
        return None

    def _trigger_book_stacks_request(self):
        """触发book_stacks API请求 - 优化版：快速触发，减少等待"""
        try:
            # 方法1：使用JavaScript直接调用API（最快方式）
            logger.debug("JavaScript直接触发API请求")
            self.webdriver_manager.driver.execute_script("""
                // 直接触发API请求
                if (window.location.pathname === '/dashboard/books') {
                    // 尝试直接调用API
                    fetch('/api/mine/book_stacks', {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    }).catch(() => {});

                    // 触发多种页面事件确保API调用
                    window.dispatchEvent(new Event('focus'));
                    window.dispatchEvent(new Event('load'));
                    window.dispatchEvent(new Event('resize'));
                    window.dispatchEvent(new Event('DOMContentLoaded'));
                }
            """)
            time.sleep(0.1)  # 极短等待时间

        except Exception as e:
            logger.warning(f"触发API请求失败: {e}")
            # 如果JavaScript失败，尝试页面刷新作为备选
            try:
                logger.debug("备选方案：页面刷新触发API请求")
                self.webdriver_manager.driver.refresh()
                time.sleep(0.2)  # 极短等待时间
            except Exception as e2:
                logger.warning(f"页面刷新也失败: {e2}")

    def _parse_intercepted_response(self, request) -> Optional[List[Dict]]:
        """解析拦截的响应数据"""
        try:
            response_body = request.response.body

            # 处理gzip压缩
            if isinstance(response_body, bytes):
                content_encoding = request.response.headers.get('Content-Encoding', '').lower()
                if 'gzip' in content_encoding:
                    import gzip
                    response_text = gzip.decompress(response_body).decode('utf-8')
                    logger.debug("成功解压gzip响应数据")
                else:
                    response_text = response_body.decode('utf-8', errors='ignore')
            else:
                response_text = str(response_body)

            data = json.loads(response_text)
            logger.debug(f"拦截到响应数据类型: {type(data)}")
            return data

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"解析响应数据失败: {e}")
            return None


    
    def get_docs(self, book_id: str) -> APIResult:
        """获取指定知识库的文档列表"""
        url = f"{YUQUE_API_BASE}{API_ENDPOINTS['docs']}?book_id={book_id}"
        logger.info(f"获取知识库 {book_id} 的文档列表...")

        result = self._make_request('GET', url)
        if result.success and result.data and 'data' in result.data:
            docs = result.data['data']
            logger.info(f"获取到 {len(docs)} 个文档")
            return APIResult.success_result(docs, result.status_code, url, 'GET')

        error_msg = f"获取知识库 {book_id} 文档列表失败"
        if not result.success:
            error_msg += f": {result.error_msg}"
        logger.error(error_msg)
        return APIResult.error_result(error_msg, result.status_code, url, 'GET')
    
    def export_doc(self, doc_id: str, doc_type: str | None = None) -> APIResult:
        """导出文档并返回下载URL"""
        url = YUQUE_API_BASE + API_ENDPOINTS["export"].format(doc_id=doc_id)

        # 根据文档类型设置导出参数
        if doc_type and doc_type.lower() == 'sheet':
            # Excel文档使用特殊的导出配置
            export_data = {
                "type": "excel",  # Excel文档使用excel类型
                "force": 0
            }
            logger.info(f"请求导出Excel文档 {doc_id} (excel格式)...")
        else:
            # 其他文档使用默认配置
            export_data = {
                "type": DOWNLOAD_CONFIG["export_type"],
                "force": DOWNLOAD_CONFIG["export_force"]
            }
            logger.info(f"请求导出文档 {doc_id} ({DOWNLOAD_CONFIG['export_type']}格式)...")

        result = self._make_request('POST', url, json=export_data)

        if not result.success:
            error_msg = f"导出请求失败: {doc_id}"
            if result.error_msg:
                error_msg += f" - {result.error_msg}"
            logger.error(error_msg)
            return APIResult.error_result(error_msg, result.status_code or 0, url, 'POST')

        # 轮询检查导出状态
        return self._poll_export_status(doc_id, result, export_data)

    def _poll_export_status(self, doc_id: str, initial_result: APIResult, export_data: dict = None) -> APIResult:
        """轮询检查导出状态"""
        max_attempts = DOWNLOAD_CONFIG["max_retries"]
        poll_interval = DOWNLOAD_CONFIG["poll_interval"]

        # 如果没有传入export_data，使用默认配置
        if export_data is None:
            export_data = {
                "type": DOWNLOAD_CONFIG["export_type"],
                "force": DOWNLOAD_CONFIG["export_force"]
            }

        # 检查初始响应
        if (initial_result.success and initial_result.data and
            initial_result.data.get('data', {}).get('state') == 'success'):
            download_url = initial_result.data['data'].get('url')
            if download_url:
                # 为Excel文档构建完整的下载URL
                if export_data.get('type') == 'excel':
                    download_url = self._construct_excel_download_url(download_url)
                logger.info(f"文档 {doc_id} 导出成功")
                return APIResult.success_result(download_url, initial_result.status_code or 200,
                                              initial_result.url, initial_result.method)

        # 开始轮询
        for attempt in range(max_attempts):
            logger.info(f"检查导出状态 {doc_id} (第 {attempt + 1}/{max_attempts} 次)")
            time.sleep(poll_interval)

            # 重新请求导出状态，使用相同的export_data
            url = YUQUE_API_BASE + API_ENDPOINTS["export"].format(doc_id=doc_id)
            result = self._make_request('POST', url, json=export_data)

            if (result.success and result.data and
                result.data.get('data', {}).get('state') == 'success'):
                download_url = result.data['data'].get('url')
                if download_url:
                    # 为Excel文档构建完整的下载URL
                    if export_data.get('type') == 'excel':
                        download_url = self._construct_excel_download_url(download_url)
                    logger.info(f"文档 {doc_id} 导出成功")
                    return APIResult.success_result(download_url, result.status_code or 200, url, 'POST')

        error_msg = f"文档 {doc_id} 导出超时"
        logger.error(error_msg)
        return APIResult.error_result(error_msg, None, initial_result.url, initial_result.method)



    def download_file(self, url: str, file_path: str) -> bool:
        """下载文件，支持重试和不同认证方式"""
        try:
            logger.info(f"开始下载文件: {file_path}")

            # 分析URL类型
            url_type = self._analyze_url_type(url)
            logger.info(f"检测到URL类型: {url_type}")

            # 根据URL类型选择不同的下载策略
            if url_type == 'oss_standard':
                # OSS签名URL使用专门的策略
                strategies = [
                    ("OSS签名URL下载", self._download_oss_signed_url),
                    ("OSS最小头部下载", self._download_oss_minimal_headers),
                    ("直接下载", self._download_direct)
                ]
            else:
                # 其他URL使用原有策略
                strategies = [
                    ("直接下载", self._download_direct),
                    ("添加语雀认证头", self._download_with_yuque_headers),
                    ("使用完整认证", self._download_with_full_auth)
                ]

            for strategy_name, download_func in strategies:
                try:
                    logger.info(f"尝试策略: {strategy_name}")
                    if download_func(url, file_path):
                        logger.info(f"文件下载成功: {file_path} (策略: {strategy_name})")
                        return True
                except Exception as e:
                    logger.warning(f"策略 {strategy_name} 失败: {e}")
                    continue

            logger.error(f"所有下载策略都失败了: {url}")
            return False

        except Exception as e:
            logger.error(f"下载文件异常 {url}: {e}")
            return False

    def _analyze_url_type(self, url: str) -> str:
        """分析URL类型"""
        try:
            import urllib.parse
            parsed = urllib.parse.urlparse(url)
            params = urllib.parse.parse_qs(parsed.query)

            if 'filename' in params and 'attachable_type' in params:
                return 'yuque_internal'
            elif 'OSSAccessKeyId' in params and 'Signature' in params:
                return 'oss_standard'
            else:
                return 'unknown'
        except:
            return 'unknown'

    def _download_direct(self, url: str, file_path: str) -> bool:
        """直接下载"""
        response = self.session.get(url, stream=True, timeout=DOWNLOAD_CONFIG["timeout"])
        response.raise_for_status()
        return self._save_response_to_file(response, file_path)

    def _download_with_yuque_headers(self, url: str, file_path: str) -> bool:
        """使用语雀认证头下载"""
        headers = {
            'Referer': 'https://www.yuque.com',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        # 添加当前session的cookies
        response = self.session.get(url, headers=headers, stream=True, timeout=DOWNLOAD_CONFIG["timeout"])
        response.raise_for_status()
        return self._save_response_to_file(response, file_path)

    def _download_with_full_auth(self, url: str, file_path: str) -> bool:
        """使用完整认证下载"""
        # 更新cookies和headers
        self._update_cookies_from_driver()
        self._update_headers_from_driver()

        headers = {
            'Referer': 'https://www.yuque.com',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/octet-stream,*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        }

        response = self.session.get(url, headers=headers, stream=True, timeout=DOWNLOAD_CONFIG["timeout"])
        response.raise_for_status()
        return self._save_response_to_file(response, file_path)

    def _save_response_to_file(self, response, file_path: str) -> bool:
        """保存响应到文件"""
        total_size = int(response.headers.get('content-length', 0))

        with open(file_path, 'wb') as f:
            downloaded = 0
            for chunk in response.iter_content(chunk_size=DOWNLOAD_CONFIG["chunk_size"]):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)

                    # 显示进度
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}%", end='', flush=True)

        print()  # 换行
        return True

    def _download_oss_signed_url(self, url: str, file_path: str) -> bool:
        """专门用于OSS签名URL的下载方法"""
        try:
            # 检查URL是否已过期
            if not self._check_oss_url_validity(url):
                logger.warning(f"OSS签名URL可能已过期: {url}")
                return False

            # 创建一个新的session，避免使用已有的认证信息
            import requests
            clean_session = requests.Session()

            # 只使用最基本的头部，避免干扰OSS签名验证
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
            }

            response = clean_session.get(url, headers=headers, stream=True, timeout=DOWNLOAD_CONFIG["timeout"])
            response.raise_for_status()
            return self._save_response_to_file(response, file_path)

        except Exception as e:
            logger.error(f"OSS签名URL下载失败: {e}")
            return False

    def _download_oss_minimal_headers(self, url: str, file_path: str) -> bool:
        """使用最小头部的OSS下载方法"""
        try:
            # 使用当前session但清除可能干扰的头部
            original_headers = dict(self.session.headers)

            # 清除可能干扰OSS签名的头部
            headers_to_remove = ['Authorization', 'Cookie', 'X-Requested-With', 'X-CSRF-Token', 'Referer']
            for header in headers_to_remove:
                self.session.headers.pop(header, None)

            # 只保留基本头部
            minimal_headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
                'Accept': '*/*'
            }

            response = self.session.get(url, headers=minimal_headers, stream=True, timeout=DOWNLOAD_CONFIG["timeout"])
            response.raise_for_status()

            # 恢复原始头部
            self.session.headers.clear()
            self.session.headers.update(original_headers)

            return self._save_response_to_file(response, file_path)

        except Exception as e:
            # 确保恢复原始头部
            self.session.headers.clear()
            self.session.headers.update(original_headers)
            logger.error(f"OSS最小头部下载失败: {e}")
            return False

    def _check_oss_url_validity(self, url: str) -> bool:
        """检查OSS签名URL是否有效（未过期）"""
        try:
            import urllib.parse
            import time

            parsed = urllib.parse.urlparse(url)
            params = urllib.parse.parse_qs(parsed.query)

            if 'Expires' in params:
                expires_timestamp = int(params['Expires'][0])
                current_timestamp = int(time.time())

                if current_timestamp >= expires_timestamp:
                    logger.warning(f"OSS URL已过期: 当前时间={current_timestamp}, 过期时间={expires_timestamp}")
                    return False
                else:
                    remaining_time = expires_timestamp - current_timestamp
                    logger.info(f"OSS URL有效，剩余时间: {remaining_time}秒")
                    return True

            return True  # 如果没有Expires参数，假设有效

        except Exception as e:
            logger.warning(f"检查OSS URL有效性失败: {e}")
            return True  # 检查失败时假设有效，继续尝试下载

    def download_file_with_retry(self, doc_id: str, url: str, file_path: str, doc_type: str | None = None) -> bool:
        """带重新导出功能的文件下载"""
        try:
            # 首先尝试正常下载
            if self.download_file(url, file_path):
                return True

            # 如果是OSS URL且下载失败，检查是否过期
            url_type = self._analyze_url_type(url)
            if url_type == 'oss_standard' and not self._check_oss_url_validity(url):
                logger.info(f"OSS URL已过期，尝试重新导出文档: {doc_id}")

                # 重新导出文档
                export_result = self.export_doc(doc_id, doc_type)
                if export_result.success and export_result.data:
                    new_url = export_result.data
                    logger.info(f"重新导出成功，新URL: {new_url}")

                    # 使用新URL下载
                    return self.download_file(new_url, file_path)
                else:
                    logger.error(f"重新导出失败: {export_result.error_msg}")
                    return False

            return False

        except Exception as e:
            logger.error(f"带重试的文件下载失败: {e}")
            return False

    def _construct_excel_download_url(self, relative_url: str) -> str:
        """为Excel文档构建完整的下载URL"""
        try:
            # 如果已经是完整URL，直接返回
            if relative_url.startswith('http'):
                return relative_url

            # 确保相对URL以/开头
            if not relative_url.startswith('/'):
                relative_url = '/' + relative_url

            # 根据当前环境构建基础URL
            current_url = self.webdriver_manager.driver.current_url
            if 'fjzx.yuque.com' in current_url:
                base_url = "https://fjzx.yuque.com"
            else:
                base_url = "https://www.yuque.com"

            # 构建完整URL，确保没有双斜杠
            full_url = base_url + relative_url
            logger.info(f"Excel文档URL构建: {relative_url} -> {full_url}")
            return full_url

        except Exception as e:
            logger.error(f"构建Excel下载URL失败: {e}")
            # 如果构建失败，返回原始URL
            return relative_url

    def close(self):
        """关闭API客户端"""
        if self.session:
            self.session.close()
            logger.info("API客户端已关闭")
