#!/usr/bin/env python3
"""
Windows 专用打包脚本
用于在 Windows 环境中构建完整的可执行包
"""

import os
import sys
import shutil
import zipfile
import subprocess
import time
from pathlib import Path

def get_timestamp():
    """获取时间戳"""
    return time.strftime("%Y%m%d_%H%M%S")

def check_environment():
    """检查构建环境"""
    print("🔧 检查构建环境...")
    
    # 检查 Python 版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要 Python 3.8 或更高版本")
        return False
    
    print(f"✅ Python 版本: {sys.version}")
    
    # 检查 PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("⚠️ 未找到 PyInstaller，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller 安装完成")
    
    # 检查必要的依赖
    required_modules = ['selenium', 'seleniumwire', 'requests', 'tqdm']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ {module}")
    
    if missing_modules:
        print(f"⚠️ 缺少依赖: {', '.join(missing_modules)}")
        print("📦 正在安装依赖...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("✅ 依赖安装完成")
    
    return True

def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   🗑️ 删除: {dir_name}")
    
    print("✅ 构建目录清理完成")

def build_executable():
    """构建可执行文件"""
    print("🏗️ 构建 Windows 可执行文件...")
    
    # 检查 spec 文件
    spec_file = "yuque_downloader_windows.spec"
    if not os.path.exists(spec_file):
        print(f"❌ 错误: 未找到 {spec_file}")
        return False
    
    # 运行 PyInstaller
    try:
        cmd = [sys.executable, "-m", "PyInstaller", spec_file, "--clean"]
        print(f"📋 执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ PyInstaller 构建成功")
            return True
        else:
            print("❌ PyInstaller 构建失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程发生错误: {e}")
        return False

def create_windows_package():
    """创建 Windows 分发包"""
    print("📦 创建 Windows 分发包...")
    
    # 检查可执行文件
    exe_path = "dist/yuque-downloader.exe"
    if not os.path.exists(exe_path):
        print(f"❌ 错误: 未找到可执行文件 {exe_path}")
        return None
    
    # 创建分发目录
    timestamp = get_timestamp()
    package_dir = f"yuque-downloader-windows-{timestamp}"
    
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    
    os.makedirs(package_dir)
    print(f"📁 创建分发目录: {package_dir}")
    
    # 复制可执行文件
    shutil.copy2(exe_path, package_dir)
    print("   ✅ yuque-downloader.exe")
    
    # 复制启动脚本
    if os.path.exists("run-yuque-downloader-windows.bat"):
        shutil.copy2("run-yuque-downloader-windows.bat", os.path.join(package_dir, "start.bat"))
        print("   ✅ start.bat")
    
    # 复制文档文件
    doc_files = ["README.md", "LICENSE", "INSTALL.md", "requirements.txt"]
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            shutil.copy2(doc_file, package_dir)
            print(f"   ✅ {doc_file}")
    
    # 创建必要目录
    dirs_to_create = ["downloads", "logs", "drivers"]
    for dir_name in dirs_to_create:
        dir_path = os.path.join(package_dir, dir_name)
        os.makedirs(dir_path, exist_ok=True)
        
        # 创建 .gitkeep 文件
        gitkeep_path = os.path.join(dir_path, ".gitkeep")
        with open(gitkeep_path, 'w') as f:
            f.write("")
        
        print(f"   ✅ {dir_name}/")
    
    # 创建用户指南
    create_windows_readme(package_dir)
    
    return package_dir

def create_windows_readme(package_dir):
    """创建 Windows 用户指南"""
    readme_content = """# 🚀 语雀下载器 Windows 版

## 快速开始

1. **双击运行** `start.bat` 文件
2. **登录语雀** 在打开的浏览器中登录您的语雀账号
3. **选择知识库** 从列表中选择要下载的知识库
4. **开始下载** 确认后自动下载到 `downloads` 目录

## 文件说明

- `yuque-downloader.exe` - 主程序（可直接运行）
- `start.bat` - 启动脚本（推荐使用）
- `downloads/` - 下载文件存储目录
- `logs/` - 程序日志目录

## 支持的文档类型

- 📝 Word 文档 (.docx)
- 📊 Excel 表格 (.xlsx)
- 📋 其他格式（PDF、Markdown等）

## 系统要求

- Windows 10 或更高版本
- Chrome 浏览器
- 网络连接

## 常见问题

**Q: 程序无法启动？**
A: 右键选择"以管理员身份运行" start.bat

**Q: 浏览器无法打开？**
A: 确保已安装 Chrome 浏览器

**Q: 下载失败？**
A: 检查网络连接和语雀账号登录状态

## 技术支持

如有问题，请查看 `logs` 目录中的日志文件。
"""
    
    readme_path = os.path.join(package_dir, "使用说明.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("   ✅ 使用说明.txt")

def create_zip_package(package_dir):
    """创建 ZIP 压缩包"""
    print("🗜️ 创建 ZIP 压缩包...")
    
    zip_name = f"{package_dir}.zip"
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, package_dir)
                zipf.write(file_path, arcname)
    
    # 获取文件大小
    size_mb = os.path.getsize(zip_name) / 1024 / 1024
    
    print(f"✅ ZIP 包创建成功!")
    print(f"📦 文件名: {zip_name}")
    print(f"📊 大小: {size_mb:.1f} MB")
    
    return zip_name

def main():
    """主函数"""
    print("🏗️ 语雀下载器 Windows 打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("main.py"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    try:
        # 检查环境
        if not check_environment():
            print("❌ 环境检查失败")
            sys.exit(1)
        
        # 清理构建目录
        clean_build_dirs()
        
        # 构建可执行文件
        if not build_executable():
            print("❌ 可执行文件构建失败")
            sys.exit(1)
        
        # 创建分发包
        package_dir = create_windows_package()
        if not package_dir:
            print("❌ 分发包创建失败")
            sys.exit(1)
        
        # 创建 ZIP 包
        zip_file = create_zip_package(package_dir)
        
        print("\n" + "=" * 50)
        print("🎉 Windows 包构建完成!")
        print("=" * 50)
        print(f"📦 ZIP 包: {zip_file}")
        print(f"📁 源目录: {package_dir}")
        print("\n📋 分发说明:")
        print("1. 将 ZIP 文件发送给 Windows 用户")
        print("2. 用户解压后双击运行 start.bat")
        print("3. 程序会自动启动并打开浏览器")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断构建过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 构建过程发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
