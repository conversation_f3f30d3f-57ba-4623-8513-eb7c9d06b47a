# Windows 构建环境 Dockerfile
# 使用 Wine 在 Linux 容器中模拟 Windows 环境

FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV WINEARCH=win64
ENV WINEPREFIX=/root/.wine

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    wget \
    software-properties-common \
    gnupg2 \
    curl \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# 添加 Wine 仓库并安装 Wine
RUN wget -nc https://dl.winehq.org/wine-builds/winehq.key && \
    apt-key add winehq.key && \
    add-apt-repository 'deb https://dl.winehq.org/wine-builds/ubuntu/ jammy main' && \
    apt-get update && \
    apt-get install -y winehq-stable && \
    rm -rf /var/lib/apt/lists/*

# 安装 Python 3.11 for Windows
RUN wget https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe && \
    wine python-3.11.7-amd64.exe /quiet InstallAllUsers=1 PrependPath=1 && \
    rm python-3.11.7-amd64.exe

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY . .

# 安装 Python 依赖
RUN wine python -m pip install --upgrade pip && \
    wine python -m pip install -r requirements.txt && \
    wine python -m pip install pyinstaller

# 构建脚本
COPY docker/build-windows.sh /build-windows.sh
RUN chmod +x /build-windows.sh

# 默认命令
CMD ["/build-windows.sh"]
