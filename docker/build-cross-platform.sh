#!/bin/bash
# 跨平台构建脚本

set -e

echo "🚀 语雀下载器跨平台构建工具"
echo "================================"

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: 未找到 Docker，请先安装 Docker"
    echo "📥 安装指令:"
    echo "   macOS: brew install docker"
    echo "   或访问: https://docs.docker.com/desktop/install/mac-install/"
    exit 1
fi

# 检查 Docker 是否运行
if ! docker info &> /dev/null; then
    echo "❌ 错误: Docker 未运行，请启动 Docker Desktop"
    exit 1
fi

echo "✅ Docker 环境检查通过"
echo

# 创建输出目录
OUTPUT_DIR="cross-platform-builds"
mkdir -p "$OUTPUT_DIR"

echo "📁 输出目录: $OUTPUT_DIR"
echo

# 构建 Windows 版本
echo "🏗️ 构建 Windows 版本..."
echo "========================"

# 构建 Docker 镜像
docker build -f docker/Dockerfile.windows -t yuque-downloader-windows-builder .

# 运行构建容器
docker run --rm -v "$(pwd)/$OUTPUT_DIR:/output" yuque-downloader-windows-builder bash -c "
    /build-windows.sh && 
    cp yuque-downloader-windows-*.zip /output/
"

if [ $? -eq 0 ]; then
    echo "✅ Windows 版本构建成功!"
else
    echo "❌ Windows 版本构建失败!"
fi

echo

# 构建 macOS 版本（本地构建）
echo "🏗️ 构建 macOS 版本..."
echo "====================="

if [[ "$OSTYPE" == "darwin"* ]]; then
    # 检查 Python 环境
    if ! command -v python3 &> /dev/null; then
        echo "❌ 错误: 未找到 Python3"
        exit 1
    fi
    
    # 安装依赖
    echo "📦 安装依赖..."
    python3 -m pip install --upgrade pip
    python3 -m pip install -r requirements.txt
    python3 -m pip install pyinstaller
    
    # 构建
    echo "🔨 构建 macOS 可执行文件..."
    python3 -m PyInstaller yuque_downloader_terminal.spec
    
    # 创建分发包
    echo "📁 创建 macOS 分发包..."
    MACOS_DIST="$OUTPUT_DIR/macos-dist"
    mkdir -p "$MACOS_DIST"
    
    cp dist/yuque-downloader "$MACOS_DIST/"
    cp run-yuque-downloader-v2.1.0.sh "$MACOS_DIST/start.sh"
    cp README.md "$MACOS_DIST/"
    cp LICENSE "$MACOS_DIST/"
    cp INSTALL.md "$MACOS_DIST/"
    cp requirements.txt "$MACOS_DIST/"
    
    mkdir -p "$MACOS_DIST/downloads"
    mkdir -p "$MACOS_DIST/logs"
    mkdir -p "$MACOS_DIST/drivers"
    
    touch "$MACOS_DIST/downloads/.gitkeep"
    touch "$MACOS_DIST/logs/.gitkeep"
    touch "$MACOS_DIST/drivers/.gitkeep"
    
    chmod +x "$MACOS_DIST/yuque-downloader"
    chmod +x "$MACOS_DIST/start.sh"
    
    # 创建压缩包
    cd "$MACOS_DIST"
    tar -czf "../yuque-downloader-macos-$(date +%Y%m%d_%H%M%S).tar.gz" *
    cd - > /dev/null
    
    echo "✅ macOS 版本构建成功!"
else
    echo "⚠️ 跳过 macOS 版本构建（当前不在 macOS 系统）"
fi

echo

# 显示构建结果
echo "🎉 构建完成!"
echo "============"
echo "📁 输出目录: $OUTPUT_DIR"
echo "📦 构建产物:"
ls -la "$OUTPUT_DIR"/*.zip "$OUTPUT_DIR"/*.tar.gz 2>/dev/null || echo "   (无构建产物)"

echo
echo "📋 使用说明:"
echo "1. Windows 用户: 下载 .zip 文件，解压后运行 start.bat"
echo "2. macOS 用户: 下载 .tar.gz 文件，解压后运行 ./start.sh"
echo "3. 所有版本都包含完整的依赖和文档"
