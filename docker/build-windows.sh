#!/bin/bash
# Windows 构建脚本

set -e

echo "🏗️ 开始构建 Windows 版本..."

# 初始化 Wine 环境
echo "🍷 初始化 Wine 环境..."
winecfg

# 构建可执行文件
echo "📦 使用 PyInstaller 构建..."
wine python -m PyInstaller yuque_downloader_windows.spec

# 检查构建结果
if [ -f "dist/yuque-downloader.exe" ]; then
    echo "✅ Windows 可执行文件构建成功!"
    
    # 创建分发目录
    echo "📁 创建分发包..."
    mkdir -p windows-dist
    
    # 复制文件
    cp dist/yuque-downloader.exe windows-dist/
    cp run-yuque-downloader-windows.bat windows-dist/start.bat
    cp README.md windows-dist/
    cp LICENSE windows-dist/
    cp INSTALL.md windows-dist/
    cp requirements.txt windows-dist/
    
    # 创建目录
    mkdir -p windows-dist/downloads
    mkdir -p windows-dist/logs
    mkdir -p windows-dist/drivers
    
    # 创建 .gitkeep 文件
    touch windows-dist/downloads/.gitkeep
    touch windows-dist/logs/.gitkeep
    touch windows-dist/drivers/.gitkeep
    
    # 创建压缩包
    cd windows-dist
    zip -r ../yuque-downloader-windows-$(date +%Y%m%d_%H%M%S).zip *
    cd ..
    
    echo "🎉 Windows 分发包构建完成!"
    ls -la yuque-downloader-windows-*.zip
    
else
    echo "❌ Windows 可执行文件构建失败!"
    exit 1
fi
