#!/bin/bash
# GitHub Actions 自动化设置脚本

set -e

echo "🚀 语雀下载器 GitHub Actions 自动化设置"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查当前目录
if [ ! -f "main.py" ]; then
    echo -e "${RED}❌ 错误: 请在项目根目录运行此脚本${NC}"
    exit 1
fi

echo -e "${BLUE}📋 检查项目文件...${NC}"

# 检查必要文件
required_files=(
    ".github/workflows/build-cross-platform.yml"
    "yuque_downloader_windows.spec"
    "yuque_downloader_terminal.spec"
    "requirements.txt"
    "main.py"
    "README.md"
    "LICENSE"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo -e "   ${GREEN}✅ $file${NC}"
    else
        echo -e "   ${RED}❌ $file${NC}"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo -e "${RED}❌ 缺少必要文件: ${missing_files[*]}${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 所有必要文件检查通过${NC}"
echo

# 检查 Git 状态
echo -e "${BLUE}🔧 检查 Git 环境...${NC}"

if [ ! -d ".git" ]; then
    echo -e "${YELLOW}⚠️ 未找到 Git 仓库，正在初始化...${NC}"
    git init
    echo -e "${GREEN}✅ Git 仓库初始化完成${NC}"
else
    echo -e "${GREEN}✅ Git 仓库已存在${NC}"
fi

# 检查 GitHub CLI
echo -e "${BLUE}🔧 检查 GitHub CLI...${NC}"
if command -v gh &> /dev/null; then
    echo -e "${GREEN}✅ GitHub CLI 已安装${NC}"
    
    # 检查登录状态
    if gh auth status &> /dev/null; then
        echo -e "${GREEN}✅ GitHub CLI 已登录${NC}"
        GITHUB_USER=$(gh api user --jq .login)
        echo -e "   👤 当前用户: ${GITHUB_USER}"
    else
        echo -e "${YELLOW}⚠️ GitHub CLI 未登录${NC}"
        echo -e "${BLUE}🔑 正在启动登录流程...${NC}"
        gh auth login
        GITHUB_USER=$(gh api user --jq .login)
        echo -e "${GREEN}✅ 登录成功，用户: ${GITHUB_USER}${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ 未找到 GitHub CLI${NC}"
    echo -e "${BLUE}📥 安装 GitHub CLI...${NC}"
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if command -v brew &> /dev/null; then
            brew install gh
        else
            echo -e "${RED}❌ 请先安装 Homebrew 或手动安装 GitHub CLI${NC}"
            echo -e "   安装地址: https://cli.github.com/"
            exit 1
        fi
    else
        echo -e "${RED}❌ 请手动安装 GitHub CLI${NC}"
        echo -e "   安装地址: https://cli.github.com/"
        exit 1
    fi
    
    echo -e "${BLUE}🔑 请登录 GitHub...${NC}"
    gh auth login
    GITHUB_USER=$(gh api user --jq .login)
    echo -e "${GREEN}✅ GitHub CLI 安装并登录成功${NC}"
fi

echo

# 询问仓库名称
echo -e "${BLUE}📝 配置仓库信息...${NC}"
read -p "请输入 GitHub 仓库名称 [yuque-downloader]: " REPO_NAME
REPO_NAME=${REPO_NAME:-yuque-downloader}

read -p "仓库是否为公开仓库？(y/n) [y]: " IS_PUBLIC
IS_PUBLIC=${IS_PUBLIC:-y}

if [[ "$IS_PUBLIC" == "y" || "$IS_PUBLIC" == "Y" ]]; then
    VISIBILITY="--public"
    echo -e "${GREEN}✅ 将创建公开仓库（可免费使用 GitHub Actions）${NC}"
else
    VISIBILITY="--private"
    echo -e "${YELLOW}⚠️ 将创建私有仓库（需要付费计划使用 GitHub Actions）${NC}"
fi

echo

# 添加文件到 Git
echo -e "${BLUE}📦 准备提交文件...${NC}"

# 检查是否有未跟踪的文件
if [ -n "$(git status --porcelain)" ]; then
    echo -e "${BLUE}📋 添加文件到 Git...${NC}"
    git add .
    
    # 创建提交
    COMMIT_MSG="初始化语雀下载器项目 - 支持 GitHub Actions 跨平台自动构建

✨ 功能特性:
- 🏗️ GitHub Actions 自动化构建
- 📦 支持 Windows (.exe)、macOS、Linux 三平台
- 🚀 版本标签自动触发构建和发布
- 📋 完整的分发包（文档、启动脚本等）
- 🔧 手动触发构建支持

🛠️ 技术栈:
- Python 3.8+
- PyInstaller
- Selenium + selenium-wire
- GitHub Actions

📁 项目结构:
- .github/workflows/ - GitHub Actions 配置
- *.spec - PyInstaller 配置文件
- docker/ - Docker 构建支持
- 完整的文档和使用指南"

    git commit -m "$COMMIT_MSG"
    echo -e "${GREEN}✅ 文件提交完成${NC}"
else
    echo -e "${GREEN}✅ 没有需要提交的更改${NC}"
fi

# 创建 GitHub 仓库
echo -e "${BLUE}🏗️ 创建 GitHub 仓库...${NC}"

# 检查仓库是否已存在
if gh repo view "$GITHUB_USER/$REPO_NAME" &> /dev/null; then
    echo -e "${YELLOW}⚠️ 仓库 $GITHUB_USER/$REPO_NAME 已存在${NC}"
    read -p "是否要推送到现有仓库？(y/n) [y]: " PUSH_EXISTING
    PUSH_EXISTING=${PUSH_EXISTING:-y}
    
    if [[ "$PUSH_EXISTING" == "y" || "$PUSH_EXISTING" == "Y" ]]; then
        # 添加远程仓库（如果不存在）
        if ! git remote get-url origin &> /dev/null; then
            git remote add origin "https://github.com/$GITHUB_USER/$REPO_NAME.git"
        fi
        
        # 推送代码
        git branch -M main
        git push -u origin main
        echo -e "${GREEN}✅ 代码推送到现有仓库完成${NC}"
    else
        echo -e "${YELLOW}⚠️ 跳过推送，请手动处理仓库${NC}"
        exit 0
    fi
else
    # 创建新仓库
    echo -e "${BLUE}🆕 创建新仓库 $REPO_NAME...${NC}"
    gh repo create "$REPO_NAME" $VISIBILITY --source=. --remote=origin --push
    echo -e "${GREEN}✅ GitHub 仓库创建并推送完成${NC}"
fi

echo

# 配置仓库设置
echo -e "${BLUE}⚙️ 配置仓库设置...${NC}"
echo -e "${YELLOW}📋 请手动完成以下设置：${NC}"
echo
echo -e "${BLUE}1. 启用 GitHub Actions:${NC}"
echo "   - 访问: https://github.com/$GITHUB_USER/$REPO_NAME/settings/actions"
echo "   - Actions permissions: 选择 'Allow all actions and reusable workflows'"
echo "   - Workflow permissions: 选择 'Read and write permissions'"
echo "   - 勾选 'Allow GitHub Actions to create and approve pull requests'"
echo
echo -e "${BLUE}2. 测试自动化构建:${NC}"
echo "   - 访问: https://github.com/$GITHUB_USER/$REPO_NAME/actions"
echo "   - 选择 '跨平台构建语雀下载器' 工作流"
echo "   - 点击 'Run workflow' 手动触发测试"
echo

# 提供后续操作命令
echo -e "${GREEN}🎉 设置完成！${NC}"
echo -e "${BLUE}📋 后续操作命令：${NC}"
echo
echo -e "${YELLOW}# 创建版本标签触发自动构建和发布${NC}"
echo "git tag v2.1.0"
echo "git push origin v2.1.0"
echo
echo -e "${YELLOW}# 手动触发构建（使用 GitHub CLI）${NC}"
echo "gh workflow run \"跨平台构建语雀下载器\" -f version=v2.1.0"
echo
echo -e "${YELLOW}# 查看构建状态${NC}"
echo "gh run list"
echo
echo -e "${YELLOW}# 查看最新 Release${NC}"
echo "gh release list"
echo

echo -e "${BLUE}🔗 重要链接：${NC}"
echo "   📦 仓库地址: https://github.com/$GITHUB_USER/$REPO_NAME"
echo "   🏗️ Actions 页面: https://github.com/$GITHUB_USER/$REPO_NAME/actions"
echo "   📋 设置页面: https://github.com/$GITHUB_USER/$REPO_NAME/settings/actions"
echo "   📦 Releases 页面: https://github.com/$GITHUB_USER/$REPO_NAME/releases"
echo

echo -e "${GREEN}✅ GitHub Actions 自动化设置完成！${NC}"
echo -e "${BLUE}💡 请按照上述说明完成 GitHub 仓库设置，然后创建版本标签测试自动构建。${NC}"
