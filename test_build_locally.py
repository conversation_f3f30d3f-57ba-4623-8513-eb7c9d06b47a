#!/usr/bin/env python3
"""
本地构建测试脚本
用于在当前系统上测试 PyInstaller 构建过程
"""

import os
import sys
import platform
import subprocess
import shutil
from pathlib import Path

def get_system_info():
    """获取系统信息"""
    return {
        'system': platform.system(),
        'machine': platform.machine(),
        'python_version': platform.python_version(),
        'architecture': platform.architecture()[0]
    }

def print_system_info():
    """打印系统信息"""
    info = get_system_info()
    print("🖥️ 系统信息:")
    print(f"   操作系统: {info['system']}")
    print(f"   架构: {info['machine']} ({info['architecture']})")
    print(f"   Python 版本: {info['python_version']}")
    print()

def check_dependencies():
    """检查依赖"""
    print("🔧 检查依赖...")
    
    required_packages = [
        'selenium', 'seleniumwire', 'requests', 
        'tqdm', 'beautifulsoup4', 'pyinstaller'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package}")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️ 缺少依赖: {', '.join(missing)}")
        print("📦 安装命令:")
        print(f"   pip install {' '.join(missing)}")
        return False
    
    print("✅ 所有依赖已满足")
    return True

def select_spec_file():
    """选择合适的 spec 文件"""
    system = platform.system().lower()
    
    if system == 'windows':
        spec_file = 'yuque_downloader_windows.spec'
    else:
        spec_file = 'yuque_downloader_terminal.spec'
    
    if not os.path.exists(spec_file):
        print(f"❌ 错误: 未找到 {spec_file}")
        return None
    
    print(f"📋 使用配置文件: {spec_file}")
    return spec_file

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ['build', 'dist']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   🗑️ 删除: {dir_name}")

def run_pyinstaller(spec_file):
    """运行 PyInstaller"""
    print("🏗️ 开始构建...")
    
    cmd = [sys.executable, '-m', 'PyInstaller', spec_file, '--clean', '--noconfirm']
    print(f"📋 执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            encoding='utf-8'
        )
        
        if result.returncode == 0:
            print("✅ 构建成功!")
            return True
        else:
            print("❌ 构建失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程发生错误: {e}")
        return False

def check_build_result():
    """检查构建结果"""
    print("🔍 检查构建结果...")
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("❌ 错误: dist 目录不存在")
        return False
    
    # 查找可执行文件
    executables = []
    for file in dist_dir.iterdir():
        if file.is_file() and (file.suffix == '.exe' or file.stat().st_mode & 0o111):
            executables.append(file)
    
    if not executables:
        print("❌ 错误: 未找到可执行文件")
        return False
    
    for exe in executables:
        size_mb = exe.stat().st_size / 1024 / 1024
        print(f"   ✅ {exe.name} ({size_mb:.1f} MB)")
    
    return True

def test_executable():
    """测试可执行文件"""
    print("🧪 测试可执行文件...")
    
    dist_dir = Path('dist')
    exe_files = []
    
    # 查找可执行文件
    for file in dist_dir.iterdir():
        if file.is_file():
            if platform.system() == 'Windows' and file.suffix == '.exe':
                exe_files.append(file)
            elif platform.system() != 'Windows' and file.stat().st_mode & 0o111:
                exe_files.append(file)
    
    if not exe_files:
        print("❌ 未找到可执行文件")
        return False
    
    exe_file = exe_files[0]
    print(f"🎯 测试文件: {exe_file}")
    
    try:
        # 测试 --help 参数
        result = subprocess.run(
            [str(exe_file), '--help'], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ 可执行文件测试通过")
            return True
        else:
            print("⚠️ 可执行文件可能有问题")
            print(f"返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ 测试超时（可能是正常的，如果程序需要用户交互）")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 语雀下载器本地构建测试")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('main.py'):
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 打印系统信息
    print_system_info()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装缺少的依赖")
        sys.exit(1)
    
    print()
    
    # 选择 spec 文件
    spec_file = select_spec_file()
    if not spec_file:
        sys.exit(1)
    
    print()
    
    try:
        # 清理构建目录
        clean_build()
        print()
        
        # 运行 PyInstaller
        if not run_pyinstaller(spec_file):
            print("\n❌ 构建失败")
            sys.exit(1)
        
        print()
        
        # 检查构建结果
        if not check_build_result():
            print("\n❌ 构建结果检查失败")
            sys.exit(1)
        
        print()
        
        # 测试可执行文件
        test_executable()
        
        print("\n" + "=" * 50)
        print("🎉 本地构建测试完成!")
        print("=" * 50)
        
        system_info = get_system_info()
        print(f"📋 构建平台: {system_info['system']} {system_info['machine']}")
        print("📁 构建产物位于 dist/ 目录")
        
        if system_info['system'] == 'Darwin' and system_info['machine'] == 'arm64':
            print("\n💡 注意: 当前在 macOS ARM64 系统上构建")
            print("   - 生成的可执行文件仅适用于 macOS ARM64")
            print("   - 要构建 Windows 版本，请使用 GitHub Actions 或 Docker")
            print("   - 参考: CROSS_PLATFORM_BUILD_GUIDE.md")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
