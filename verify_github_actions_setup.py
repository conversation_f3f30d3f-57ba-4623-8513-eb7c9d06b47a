#!/usr/bin/env python3
"""
GitHub Actions 设置验证脚本
检查所有必要的文件和配置是否正确
"""

import os
import sys
import json
from pathlib import Path

def print_status(message, status="info"):
    """打印状态信息"""
    colors = {
        "success": "\033[92m✅",
        "error": "\033[91m❌", 
        "warning": "\033[93m⚠️",
        "info": "\033[94mℹ️"
    }
    reset = "\033[0m"
    print(f"{colors.get(status, colors['info'])} {message}{reset}")

def check_file_exists(file_path, description=""):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print_status(f"{description or file_path}", "success")
        return True
    else:
        print_status(f"{description or file_path} - 文件不存在", "error")
        return False

def check_github_actions_config():
    """检查 GitHub Actions 配置文件"""
    print("\n🔧 检查 GitHub Actions 配置...")

    workflow_file = ".github/workflows/build-cross-platform.yml"
    if not check_file_exists(workflow_file, "GitHub Actions 工作流配置"):
        return False

    try:
        with open(workflow_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 简单的文本检查（不依赖 yaml 模块）
        checks = {
            "版本标签触发": "tags:" in content and "- 'v*'" in content,
            "手动触发": "workflow_dispatch:" in content,
            "Windows 构建": "build-windows:" in content and "windows-latest" in content,
            "macOS 构建": "build-macos:" in content and "macos-latest" in content,
            "Linux 构建": "build-linux:" in content and "ubuntu-latest" in content,
            "自动发布": "create-release:" in content and "softprops/action-gh-release" in content
        }

        all_good = True
        for check_name, check_result in checks.items():
            if check_result:
                print_status(f"{check_name} 配置正确", "success")
            else:
                print_status(f"{check_name} 配置缺失", "error")
                all_good = False

        return all_good

    except Exception as e:
        print_status(f"读取配置文件失败: {e}", "error")
        return False

def check_pyinstaller_specs():
    """检查 PyInstaller 配置文件"""
    print("\n📦 检查 PyInstaller 配置...")
    
    specs = {
        "yuque_downloader_windows.spec": "Windows 构建配置",
        "yuque_downloader_terminal.spec": "macOS/Linux 构建配置"
    }
    
    all_good = True
    for spec_file, description in specs.items():
        if check_file_exists(spec_file, description):
            # 检查 spec 文件内容
            try:
                with open(spec_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键配置
                if 'Analysis' in content:
                    print_status(f"  {spec_file} - Analysis 配置存在", "success")
                else:
                    print_status(f"  {spec_file} - 缺少 Analysis 配置", "error")
                    all_good = False
                
                if 'EXE' in content:
                    print_status(f"  {spec_file} - EXE 配置存在", "success")
                else:
                    print_status(f"  {spec_file} - 缺少 EXE 配置", "error")
                    all_good = False
                    
            except Exception as e:
                print_status(f"  {spec_file} - 读取失败: {e}", "error")
                all_good = False
        else:
            all_good = False
    
    return all_good

def check_dependencies():
    """检查依赖文件"""
    print("\n📋 检查依赖配置...")
    
    if not check_file_exists("requirements.txt", "Python 依赖配置"):
        return False
    
    try:
        with open("requirements.txt", 'r', encoding='utf-8') as f:
            requirements = f.read()
        
        # 检查关键依赖
        key_deps = ['selenium', 'selenium-wire', 'requests', 'tqdm']
        missing_deps = []
        
        for dep in key_deps:
            if dep.lower() in requirements.lower():
                print_status(f"  依赖 {dep} 已配置", "success")
            else:
                print_status(f"  缺少依赖 {dep}", "warning")
                missing_deps.append(dep)
        
        if missing_deps:
            print_status(f"建议添加缺少的依赖: {', '.join(missing_deps)}", "warning")
        
        return True
        
    except Exception as e:
        print_status(f"读取 requirements.txt 失败: {e}", "error")
        return False

def check_startup_scripts():
    """检查启动脚本"""
    print("\n🚀 检查启动脚本...")
    
    scripts = {
        "run-yuque-downloader-windows.bat": "Windows 启动脚本",
        "run-yuque-downloader-v2.1.0.sh": "macOS/Linux 启动脚本"
    }
    
    all_good = True
    for script, description in scripts.items():
        if not check_file_exists(script, description):
            all_good = False
    
    return all_good

def check_documentation():
    """检查文档文件"""
    print("\n📚 检查文档文件...")
    
    docs = {
        "README.md": "项目说明文档",
        "LICENSE": "许可证文件",
        "INSTALL.md": "安装说明",
        ".gitignore": "Git 忽略配置",
        "GITHUB_ACTIONS_SETUP_GUIDE.md": "GitHub Actions 设置指南"
    }
    
    all_good = True
    for doc, description in docs.items():
        if not check_file_exists(doc, description):
            if doc in ["LICENSE", "INSTALL.md"]:
                print_status(f"  建议创建 {doc}", "warning")
            else:
                all_good = False
    
    return all_good

def check_core_files():
    """检查核心代码文件"""
    print("\n🐍 检查核心代码文件...")
    
    core_files = [
        "main.py",
        "yuque_downloader.py",
        "config.py",
        "requirements.txt"
    ]
    
    all_good = True
    for file in core_files:
        if not check_file_exists(file, f"核心文件 {file}"):
            all_good = False
    
    return all_good

def check_git_setup():
    """检查 Git 设置"""
    print("\n🔧 检查 Git 设置...")
    
    if os.path.exists(".git"):
        print_status("Git 仓库已初始化", "success")
        
        # 检查是否有远程仓库
        try:
            import subprocess
            result = subprocess.run(['git', 'remote', '-v'], 
                                  capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                print_status("Git 远程仓库已配置", "success")
                print(f"  远程仓库: {result.stdout.strip().split()[1]}")
            else:
                print_status("未配置 Git 远程仓库", "warning")
                print_status("  运行 setup_github_actions.sh 来自动配置", "info")
        except Exception:
            print_status("无法检查 Git 远程仓库状态", "warning")
        
        return True
    else:
        print_status("Git 仓库未初始化", "warning")
        print_status("  运行 setup_github_actions.sh 来自动初始化", "info")
        return False

def generate_summary(results):
    """生成检查结果摘要"""
    print("\n" + "="*50)
    print("📋 检查结果摘要")
    print("="*50)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    
    if passed_checks == total_checks:
        print_status("🎉 所有检查通过！项目已准备好使用 GitHub Actions", "success")
        print("\n📋 下一步操作:")
        print("1. 运行 ./setup_github_actions.sh 来设置 GitHub 仓库")
        print("2. 或手动推送代码到 GitHub 并配置 Actions 权限")
        print("3. 创建版本标签测试自动构建: git tag v2.1.0 && git push origin v2.1.0")
    else:
        print_status("⚠️ 部分检查未通过，请修复问题后重新检查", "warning")
        print("\n🔧 建议操作:")
        
        failed_items = [item for item, passed in results.items() if not passed]
        for item in failed_items:
            print(f"- 修复 {item} 相关问题")
        
        print("- 运行 ./setup_github_actions.sh 来自动修复部分问题")

def main():
    """主函数"""
    print("🔍 GitHub Actions 设置验证工具")
    print("="*50)
    
    # 检查当前目录
    if not os.path.exists("main.py"):
        print_status("请在项目根目录运行此脚本", "error")
        sys.exit(1)
    
    # 执行各项检查
    results = {
        "核心文件": check_core_files(),
        "GitHub Actions 配置": check_github_actions_config(),
        "PyInstaller 配置": check_pyinstaller_specs(),
        "依赖配置": check_dependencies(),
        "启动脚本": check_startup_scripts(),
        "文档文件": check_documentation(),
        "Git 设置": check_git_setup()
    }
    
    # 生成摘要
    generate_summary(results)

if __name__ == "__main__":
    main()
