# -*- mode: python ; coding: utf-8 -*-
"""
Windows版本的PyInstaller配置文件 - 优化版
用于构建Windows .exe可执行文件
支持跨平台构建和完整依赖打包
"""

import os
import sys
import glob
import seleniumwire
from pathlib import Path

block_cipher = None

# 获取seleniumwire的资源文件路径
seleniumwire_path = Path(seleniumwire.__file__).parent
ca_crt_path = seleniumwire_path / 'ca.crt'
ca_key_path = seleniumwire_path / 'ca.key'

# 检查证书文件是否存在
if not ca_crt_path.exists():
    print(f"警告: 未找到 seleniumwire 证书文件: {ca_crt_path}")
if not ca_key_path.exists():
    print(f"警告: 未找到 seleniumwire 密钥文件: {ca_key_path}")

# 获取所有Python文件
python_files = []
for py_file in glob.glob('*.py'):
    if not py_file.startswith('__') and not py_file.startswith('test_'):  # 排除测试文件
        python_files.append((py_file, '.'))

# 收集所有Python文件和数据文件
a = Analysis(
    ['main.py'],
    pathex=['.'],
    binaries=[],
    datas=[
        # 配置和文档文件
        ('requirements.txt', '.'),
        ('README.md', '.'),
        ('LICENSE', '.'),
        ('INSTALL.md', '.'),
        # 核心Python模块
        ('config.py', '.'),
        ('yuque_downloader.py', '.'),
        ('yuque_webdriver_manager.py', '.'),
        ('yuque_books_interceptor.py', '.'),
        ('user_interface.py', '.'),
        ('api_client.py', '.'),
        ('utils.py', '.'),
        ('path_manager.py', '.'),
        ('file_manager.py', '.'),
        ('exceptions.py', '.'),
        ('__init__.py', '.'),
        ('run.py', '.'),
        ('sync_auth.py', '.'),
        ('api_result.py', '.'),
        ('enhanced_main.py', '.'),
        # seleniumwire证书文件（如果存在）
    ] + ([
        (str(ca_crt_path), 'seleniumwire/'),
        (str(ca_key_path), 'seleniumwire/'),
    ] if ca_crt_path.exists() and ca_key_path.exists() else []) + python_files,
    hiddenimports=[
        'selenium',
        'seleniumwire',
        'seleniumwire.proxy',
        'seleniumwire.proxy.proxy',
        'seleniumwire.proxy.storage',
        'seleniumwire.proxy.handler',
        'seleniumwire.proxy.modifier',
        'seleniumwire.proxy.interceptor',
        'seleniumwire.proxy.utils',
        'seleniumwire.webdriver',
        'seleniumwire.inspect',
        'seleniumwire.utils',
        'requests',
        'tqdm',
        'beautifulsoup4',
        'urllib3',
        'certifi',
        'charset_normalizer',
        'idna',
        'websocket-client',
        'trio',
        'trio_websocket',
        'attrs',
        'sortedcontainers',
        'outcome',
        'sniffio',
        'h11',
        'wsproto',
        'typing_extensions',
        'webdriver_manager',
        'webdriver_manager.chrome',
        'json',
        'pathlib',
        'logging',
        'time',
        'os',
        'sys',
        'platform',
        'OpenSSL',
        'cryptography',
        'datetime',
        'hashlib',
        'shutil',
        'zipfile',
        'tempfile'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'PIL',
        'cv2',
        'scipy',
        'jupyter',
        'notebook',
        'IPython'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 生成Windows单个可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='yuque-downloader',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Windows控制台应用
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加.ico图标文件
    version=None  # 可以添加版本信息文件
)
